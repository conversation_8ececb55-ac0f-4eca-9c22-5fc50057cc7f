"""
Backtesting API endpoints.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api.dependencies import get_data_service, validate_symbol
from app.api.schemas.backtesting import (
    BacktestRequest,
    BacktestResponse,
    BacktestResults,
    StrategiesListResponse,
    StrategyInfo,
    BacktestHistoryResponse,
    BacktestHistoryItem,
    SaveBacktestRequest,
    SaveBacktestResponse,
    StrategyComparisonRequest,
    StrategyComparisonResponse,
    BacktestValidationRequest,
    BacktestValidationResponse
)
from app.services.backtest_service import BacktestService
from app.services.backtesting.advanced_engine import AdvancedBacktestEngine, BacktestConfig
from app.services.backtesting.optimization import StrategyOptimizer, ParameterRange
from app.database.connection import get_db
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


def get_backtest_service(db: Session = Depends(get_db)) -> BacktestService:
    """Get backtest service instance."""
    return BacktestService(db)


@router.get("/strategies", response_model=StrategiesListResponse)
async def get_available_strategies(
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Get list of available trading strategies."""
    try:
        strategies = backtest_service.get_available_strategies()
        
        strategy_list = [
            StrategyInfo(**strategy) for strategy in strategies
        ]
        
        return StrategiesListResponse(strategies=strategy_list)
        
    except Exception as e:
        logger.error(f"Error getting available strategies: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve available strategies"
        )


@router.post("/run", response_model=BacktestResponse)
async def run_backtest(
    request: BacktestRequest,
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Run a backtest with specified parameters."""
    try:
        # Validate inputs
        symbol = validate_symbol(request.symbol)
        
        if request.start_date >= request.end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        if request.initial_cash <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Initial cash must be positive"
            )
        
        # Run backtest
        results = backtest_service.run_backtest(
            strategy_type=request.strategy_type,
            symbol=symbol,
            start_date=request.start_date,
            end_date=request.end_date,
            timeframe=request.timeframe,
            initial_cash=request.initial_cash,
            commission_rate=request.commission_rate,
            strategy_parameters=request.strategy_parameters,
            position_size_method=request.position_size_method,
            position_size_value=request.position_size_value
        )
        
        if not results:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Backtest execution failed"
            )
        
        # Convert results to response format
        backtest_results = BacktestResults(**results)
        
        return BacktestResponse(results=backtest_results)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running backtest: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute backtest"
        )


@router.post("/validate", response_model=BacktestValidationResponse)
async def validate_backtest_data(
    request: BacktestValidationRequest,
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Validate data availability for backtesting."""
    try:
        symbol = validate_symbol(request.symbol)
        
        issues = []
        recommendations = []
        
        # Check date range
        if request.start_date >= request.end_date:
            issues.append("Start date must be before end date")
        
        # Check if date range is too short
        days_diff = (request.end_date - request.start_date).days
        if days_diff < 30:
            recommendations.append("Consider using a longer time period (at least 30 days) for more reliable results")
        
        # Get market data to validate availability
        data = backtest_service._get_market_data(
            symbol, request.start_date, request.end_date, request.timeframe
        )
        
        data_points = 0
        if data is not None:
            data_points = len(data)
            
            if data_points < 50:
                issues.append(f"Insufficient data points ({data_points}). Need at least 50 for reliable backtesting")
            elif data_points < 100:
                recommendations.append("More data points would improve backtest reliability")
        else:
            issues.append(f"No market data available for {symbol}")
        
        # Check timeframe appropriateness
        if request.timeframe == "1m" and days_diff > 30:
            recommendations.append("1-minute data for long periods may be slow. Consider using higher timeframes")
        
        return BacktestValidationResponse(
            is_valid=len(issues) == 0,
            data_points=data_points,
            date_range={
                "start": request.start_date.isoformat(),
                "end": request.end_date.isoformat()
            },
            issues=issues,
            recommendations=recommendations
        )
        
    except Exception as e:
        logger.error(f"Error validating backtest data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate backtest data"
        )


@router.post("/save", response_model=SaveBacktestResponse)
async def save_backtest_results(
    request: SaveBacktestRequest,
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Save backtest results to database."""
    try:
        success = backtest_service.save_backtest_results(
            strategy_id=request.strategy_id,
            symbol=request.symbol,
            results=request.results
        )
        
        return SaveBacktestResponse(
            saved=success,
            backtest_id=request.strategy_id if success else None
        )
        
    except Exception as e:
        logger.error(f"Error saving backtest results: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save backtest results"
        )


@router.get("/history", response_model=BacktestHistoryResponse)
async def get_backtest_history(
    strategy_id: Optional[int] = Query(None),
    symbol: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=200),
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Get backtest execution history."""
    try:
        if symbol:
            symbol = validate_symbol(symbol)
        
        history = backtest_service.get_backtest_history(
            strategy_id=strategy_id,
            symbol=symbol,
            limit=limit
        )
        
        history_items = [
            BacktestHistoryItem(**item) for item in history
        ]
        
        return BacktestHistoryResponse(
            history=history_items,
            total_count=len(history_items)
        )
        
    except Exception as e:
        logger.error(f"Error getting backtest history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve backtest history"
        )


@router.post("/compare", response_model=StrategyComparisonResponse)
async def compare_strategies(
    request: StrategyComparisonRequest,
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Compare multiple strategies on the same data."""
    try:
        symbol = validate_symbol(request.symbol)
        
        if len(request.strategies) < 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least 2 strategies required for comparison"
            )
        
        if len(request.strategies) > 5:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 5 strategies allowed for comparison"
            )
        
        # Run comparison
        comparison_results = backtest_service.compare_strategies(
            strategy_configs=request.strategies,
            symbol=symbol,
            start_date=request.start_date,
            end_date=request.end_date,
            timeframe=request.timeframe
        )
        
        if 'error' in comparison_results:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=comparison_results['error']
            )
        
        return StrategyComparisonResponse(**comparison_results)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing strategies: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to compare strategies"
        )


@router.get("/performance-metrics")
async def get_performance_metrics_info():
    """Get information about available performance metrics."""
    try:
        metrics_info = {
            "basic_metrics": [
                "total_return", "annualized_return", "total_pnl", 
                "realized_pnl", "unrealized_pnl", "initial_capital", "final_value"
            ],
            "risk_metrics": [
                "volatility", "downside_deviation", "var_95", "cvar_95", "max_drawdown"
            ],
            "trade_metrics": [
                "total_trades", "winning_trades", "losing_trades", "win_rate",
                "avg_trade_pnl", "profit_factor", "avg_trade_duration"
            ],
            "ratios": [
                "sharpe_ratio", "sortino_ratio", "calmar_ratio", "information_ratio"
            ],
            "descriptions": {
                "sharpe_ratio": "Risk-adjusted return measure",
                "sortino_ratio": "Downside risk-adjusted return",
                "max_drawdown": "Maximum peak-to-trough decline",
                "profit_factor": "Gross profit / Gross loss",
                "win_rate": "Percentage of profitable trades",
                "var_95": "Value at Risk (95% confidence)",
                "calmar_ratio": "Annual return / Maximum drawdown"
            }
        }
        
        return {
            "success": True,
            "metrics_info": metrics_info
        }
        
    except Exception as e:
        logger.error(f"Error getting performance metrics info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance metrics information"
        )


@router.post("/advanced/run")
async def run_advanced_backtest(
    strategy_name: str,
    symbol: str,
    start_date: datetime,
    end_date: datetime,
    parameters: Dict[str, Any] = {},
    config: Dict[str, Any] = {},
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Run advanced backtest with enhanced features."""
    try:
        # Create backtest configuration
        backtest_config = BacktestConfig(**config) if config else BacktestConfig()

        # Get historical data
        data_service = backtest_service.data_service
        historical_data = data_service.get_ohlcv_data(
            symbol=symbol,
            start_time=start_date,
            end_time=end_date,
            as_dataframe=True
        )

        if historical_data is None or historical_data.empty:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No historical data found for {symbol}"
            )

        # Get strategy class
        strategy_classes = backtest_service.STRATEGY_CLASSES
        if strategy_name not in strategy_classes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown strategy: {strategy_name}"
            )

        # Create strategy instance
        strategy_class = strategy_classes[strategy_name]
        strategy = strategy_class(**parameters)

        # Run advanced backtest
        engine = AdvancedBacktestEngine(backtest_config)
        result = engine.run_backtest(strategy, historical_data)

        return {
            "success": True,
            "result": result.to_dict(),
            "config": config
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running advanced backtest: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to run advanced backtest"
        )


@router.post("/optimize")
async def optimize_strategy(
    strategy_name: str,
    symbol: str,
    start_date: datetime,
    end_date: datetime,
    parameter_ranges: Dict[str, Dict[str, Any]],
    optimization_metric: str = "sharpe_ratio",
    max_workers: int = 4,
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Optimize strategy parameters."""
    try:
        # Get historical data
        data_service = backtest_service.data_service
        historical_data = data_service.get_ohlcv_data(
            symbol=symbol,
            start_time=start_date,
            end_time=end_date,
            as_dataframe=True
        )

        if historical_data is None or historical_data.empty:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No historical data found for {symbol}"
            )

        # Get strategy class
        strategy_classes = backtest_service.STRATEGY_CLASSES
        if strategy_name not in strategy_classes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown strategy: {strategy_name}"
            )

        strategy_class = strategy_classes[strategy_name]

        # Convert parameter ranges
        param_ranges = {}
        for param_name, range_config in parameter_ranges.items():
            param_ranges[param_name] = ParameterRange(**range_config)

        # Run optimization
        optimizer = StrategyOptimizer()
        optimization_result = optimizer.optimize_parameters(
            strategy_class=strategy_class,
            data=historical_data,
            parameter_ranges=param_ranges,
            optimization_metric=optimization_metric,
            max_workers=max_workers
        )

        return {
            "success": True,
            "optimization_summary": optimization_result.get_summary(),
            "best_result": optimization_result.best_result.to_dict() if optimization_result.best_result else None,
            "total_combinations": len(optimization_result.all_results)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error optimizing strategy: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to optimize strategy"
        )


@router.post("/walk-forward")
async def walk_forward_analysis(
    strategy_name: str,
    symbol: str,
    start_date: datetime,
    end_date: datetime,
    parameter_ranges: Dict[str, Dict[str, Any]],
    optimization_window: int = 252,
    reoptimization_frequency: int = 63,
    optimization_metric: str = "sharpe_ratio",
    backtest_service: BacktestService = Depends(get_backtest_service)
):
    """Perform walk-forward analysis."""
    try:
        # Get historical data
        data_service = backtest_service.data_service
        historical_data = data_service.get_ohlcv_data(
            symbol=symbol,
            start_time=start_date,
            end_time=end_date,
            as_dataframe=True
        )

        if historical_data is None or historical_data.empty:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No historical data found for {symbol}"
            )

        # Get strategy class
        strategy_classes = backtest_service.STRATEGY_CLASSES
        if strategy_name not in strategy_classes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown strategy: {strategy_name}"
            )

        strategy_class = strategy_classes[strategy_name]

        # Convert parameter ranges
        param_ranges = {}
        for param_name, range_config in parameter_ranges.items():
            param_ranges[param_name] = ParameterRange(**range_config)

        # Run walk-forward analysis
        optimizer = StrategyOptimizer()
        wf_results = optimizer.walk_forward_analysis(
            strategy_class=strategy_class,
            data=historical_data,
            parameter_ranges=param_ranges,
            optimization_window=optimization_window,
            reoptimization_frequency=reoptimization_frequency,
            optimization_metric=optimization_metric
        )

        return {
            "success": True,
            "walk_forward_results": wf_results,
            "total_periods": len(wf_results['periods'])
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in walk-forward analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform walk-forward analysis"
        )
