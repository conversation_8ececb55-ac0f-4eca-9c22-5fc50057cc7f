"""
Paper trading portfolio management.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass, field
import uuid

from .positions import PaperPosition, PositionSide
from .orders import PaperOrder, PaperOrderManager, PaperOrderSide, PaperOrderType
from app.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class PaperTrade:
    """Completed paper trade record."""
    
    id: str
    symbol: str
    side: str
    quantity: int
    entry_price: float
    exit_price: float
    entry_time: datetime
    exit_time: datetime
    pnl: float
    commission: float
    duration_seconds: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration_days(self) -> float:
        """Get trade duration in days."""
        return self.duration_seconds / 86400
    
    @property
    def return_percentage(self) -> float:
        """Get trade return percentage."""
        cost_basis = self.quantity * self.entry_price
        if cost_basis == 0:
            return 0.0
        return (self.pnl / cost_basis) * 100
    
    @property
    def is_profitable(self) -> bool:
        """Check if trade is profitable."""
        return self.pnl > 0


class PaperPortfolio:
    """Paper trading portfolio."""
    
    def __init__(self, initial_cash: float = 100000.0, commission_rate: float = 0.001):
        """
        Initialize paper portfolio.
        
        Args:
            initial_cash: Initial cash amount
            commission_rate: Commission rate (as decimal)
        """
        self.initial_cash = initial_cash
        self.cash = initial_cash
        self.commission_rate = commission_rate
        
        # Portfolio components
        self.positions: Dict[str, PaperPosition] = {}
        self.order_manager = PaperOrderManager(commission_rate)
        self.trades: List[PaperTrade] = []
        
        # Performance tracking
        self.total_commission_paid = 0.0
        self.realized_pnl = 0.0
        self.peak_value = initial_cash
        self.max_drawdown = 0.0
        
        # Current market prices
        self.current_prices: Dict[str, float] = {}
        
        # Portfolio history
        self.value_history: List[Dict[str, Any]] = []
    
    @property
    def positions_value(self) -> float:
        """Get total value of all positions."""
        total = 0.0
        for symbol, position in self.positions.items():
            if symbol in self.current_prices:
                position.update_price(self.current_prices[symbol])
                total += position.market_value
        return total
    
    @property
    def total_value(self) -> float:
        """Get total portfolio value."""
        return self.cash + self.positions_value
    
    @property
    def unrealized_pnl(self) -> float:
        """Get total unrealized P&L."""
        return sum(pos.unrealized_pnl for pos in self.positions.values())
    
    @property
    def total_pnl(self) -> float:
        """Get total P&L (realized + unrealized)."""
        return self.realized_pnl + self.unrealized_pnl
    
    @property
    def total_return(self) -> float:
        """Get total return percentage."""
        if self.initial_cash == 0:
            return 0.0
        return ((self.total_value - self.initial_cash) / self.initial_cash) * 100
    
    def update_prices(self, prices: Dict[str, float], timestamp: datetime = None) -> None:
        """Update current prices and process orders."""
        timestamp = timestamp or datetime.utcnow()
        self.current_prices.update(prices)
        
        # Update position prices
        for symbol, position in self.positions.items():
            if symbol in prices:
                position.update_price(prices[symbol], timestamp)
        
        # Process pending orders
        filled_orders = []
        for symbol, price in prices.items():
            orders = self.order_manager.process_market_data(symbol, price, timestamp)
            filled_orders.extend(orders)
        
        # Execute filled orders
        for order in filled_orders:
            self._execute_order(order, timestamp)
        
        # Update portfolio metrics
        self._update_metrics(timestamp)
    
    def place_order(
        self,
        symbol: str,
        side: str,
        order_type: str,
        quantity: int,
        price: Optional[float] = None,
        stop_price: Optional[float] = None
    ) -> Optional[PaperOrder]:
        """Place a paper trading order."""
        try:
            # Convert string parameters to enums
            order_side = PaperOrderSide(side.lower())
            order_type_enum = PaperOrderType(order_type.lower())
            
            # Validate order
            if not self._validate_order(symbol, order_side, quantity, price):
                return None
            
            # Create order
            order = self.order_manager.create_order(
                symbol=symbol,
                side=order_side,
                order_type=order_type_enum,
                quantity=quantity,
                price=price,
                stop_price=stop_price
            )
            
            # For market orders, try immediate execution if price is available
            if order_type_enum == PaperOrderType.MARKET and symbol in self.current_prices:
                current_price = self.current_prices[symbol]
                commission = self.order_manager._calculate_commission(order, current_price)
                
                if order.fill(quantity, current_price, datetime.utcnow(), commission):
                    self._execute_order(order)
            
            return order
            
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order."""
        return self.order_manager.cancel_order(order_id)
    
    def cancel_all_orders(self, symbol: str = None) -> int:
        """Cancel all pending orders."""
        return self.order_manager.cancel_all_orders(symbol)
    
    def close_position(self, symbol: str, quantity: Optional[int] = None) -> bool:
        """Close a position (partially or completely)."""
        if symbol not in self.positions:
            logger.warning(f"No position found for {symbol}")
            return False
        
        position = self.positions[symbol]
        close_quantity = quantity or abs(position.quantity)
        
        # Determine order side (opposite of position)
        if position.is_long:
            side = "sell"
        else:
            side = "buy"
        
        # Place market order to close position
        order = self.place_order(
            symbol=symbol,
            side=side,
            order_type="market",
            quantity=close_quantity
        )
        
        return order is not None
    
    def close_all_positions(self) -> int:
        """Close all open positions."""
        closed_count = 0
        
        for symbol in list(self.positions.keys()):
            if self.close_position(symbol):
                closed_count += 1
        
        return closed_count
    
    def _validate_order(self, symbol: str, side: PaperOrderSide, quantity: int, price: Optional[float]) -> bool:
        """Validate order parameters."""
        if quantity <= 0:
            logger.error("Order quantity must be positive")
            return False
        
        if side == PaperOrderSide.BUY:
            # Check if we have enough cash
            estimated_cost = quantity * (price or self.current_prices.get(symbol, 0))
            commission = estimated_cost * self.commission_rate
            total_cost = estimated_cost + commission
            
            if total_cost > self.cash:
                logger.error(f"Insufficient cash: need {total_cost}, have {self.cash}")
                return False
        
        elif side == PaperOrderSide.SELL:
            # Check if we have enough shares
            if symbol in self.positions:
                position = self.positions[symbol]
                if position.is_long and position.quantity < quantity:
                    logger.error(f"Insufficient shares: need {quantity}, have {position.quantity}")
                    return False
            else:
                logger.error(f"No position to sell for {symbol}")
                return False
        
        return True
    
    def _execute_order(self, order: PaperOrder, timestamp: datetime = None) -> None:
        """Execute a filled order."""
        timestamp = timestamp or datetime.utcnow()
        
        try:
            if order.is_buy:
                self._execute_buy_order(order, timestamp)
            else:
                self._execute_sell_order(order, timestamp)
            
            self.total_commission_paid += order.commission
            
        except Exception as e:
            logger.error(f"Error executing order {order.id}: {e}")
    
    def _execute_buy_order(self, order: PaperOrder, timestamp: datetime) -> None:
        """Execute a buy order."""
        total_cost = order.total_value + order.commission
        self.cash -= total_cost
        
        if order.symbol in self.positions:
            # Add to existing position
            position = self.positions[order.symbol]
            if position.is_long:
                position.add_to_position(order.filled_quantity, order.avg_fill_price, order.commission)
            else:
                # Closing short position
                if order.filled_quantity >= abs(position.quantity):
                    # Completely closing short position
                    realized_pnl = position.close_position(order.avg_fill_price, order.commission)
                    self.realized_pnl += realized_pnl
                    
                    # Create trade record
                    self._create_trade_record(position, order.avg_fill_price, timestamp)
                    
                    # Remove position if completely closed
                    remaining_quantity = order.filled_quantity - abs(position.quantity)
                    del self.positions[order.symbol]
                    
                    # If there are remaining shares, create new long position
                    if remaining_quantity > 0:
                        self._create_new_position(order.symbol, PositionSide.LONG, 
                                                remaining_quantity, order.avg_fill_price, timestamp)
                else:
                    # Partially closing short position
                    realized_pnl = position.reduce_position(order.filled_quantity, order.avg_fill_price, order.commission)
                    self.realized_pnl += realized_pnl
        else:
            # Create new long position
            self._create_new_position(order.symbol, PositionSide.LONG, 
                                    order.filled_quantity, order.avg_fill_price, timestamp)
    
    def _execute_sell_order(self, order: PaperOrder, timestamp: datetime) -> None:
        """Execute a sell order."""
        proceeds = order.total_value - order.commission
        self.cash += proceeds
        
        if order.symbol in self.positions:
            position = self.positions[order.symbol]
            
            if position.is_long:
                # Closing long position
                if order.filled_quantity >= position.quantity:
                    # Completely closing long position
                    realized_pnl = position.close_position(order.avg_fill_price, order.commission)
                    self.realized_pnl += realized_pnl
                    
                    # Create trade record
                    self._create_trade_record(position, order.avg_fill_price, timestamp)
                    
                    # Remove position
                    del self.positions[order.symbol]
                else:
                    # Partially closing long position
                    realized_pnl = position.reduce_position(order.filled_quantity, order.avg_fill_price, order.commission)
                    self.realized_pnl += realized_pnl
            else:
                # Adding to short position
                position.add_to_position(-order.filled_quantity, order.avg_fill_price, order.commission)
        else:
            # Create new short position
            self._create_new_position(order.symbol, PositionSide.SHORT, 
                                    -order.filled_quantity, order.avg_fill_price, timestamp)
    
    def _create_new_position(self, symbol: str, side: PositionSide, quantity: int, price: float, timestamp: datetime) -> None:
        """Create a new position."""
        position_id = str(uuid.uuid4())
        
        position = PaperPosition(
            id=position_id,
            symbol=symbol,
            side=side,
            quantity=abs(quantity) if side == PositionSide.LONG else -abs(quantity),
            avg_price=price,
            current_price=price,
            entry_timestamp=timestamp,
            last_update=timestamp
        )
        
        self.positions[symbol] = position
        logger.info(f"Created new {side.value} position: {symbol} x {quantity} @ {price}")
    
    def _create_trade_record(self, position: PaperPosition, exit_price: float, exit_time: datetime) -> None:
        """Create a trade record for a closed position."""
        trade_id = str(uuid.uuid4())
        
        duration = (exit_time - position.entry_timestamp).total_seconds()
        
        # Calculate P&L
        if position.is_long:
            pnl = (exit_price - position.avg_price) * abs(position.quantity) - position.commission_paid
        else:
            pnl = (position.avg_price - exit_price) * abs(position.quantity) - position.commission_paid
        
        trade = PaperTrade(
            id=trade_id,
            symbol=position.symbol,
            side=position.side.value,
            quantity=abs(position.quantity),
            entry_price=position.avg_price,
            exit_price=exit_price,
            entry_time=position.entry_timestamp,
            exit_time=exit_time,
            pnl=pnl,
            commission=position.commission_paid,
            duration_seconds=duration
        )
        
        self.trades.append(trade)
        logger.info(f"Created trade record: {trade.symbol} P&L: {trade.pnl:.2f}")
    
    def _update_metrics(self, timestamp: datetime) -> None:
        """Update portfolio performance metrics."""
        current_value = self.total_value
        
        # Update peak value and drawdown
        if current_value > self.peak_value:
            self.peak_value = current_value
        
        if self.peak_value > 0:
            current_drawdown = ((self.peak_value - current_value) / self.peak_value) * 100
            if current_drawdown > self.max_drawdown:
                self.max_drawdown = current_drawdown
        
        # Record value history
        self.value_history.append({
            'timestamp': timestamp,
            'total_value': current_value,
            'cash': self.cash,
            'positions_value': self.positions_value,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'total_pnl': self.total_pnl
        })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get portfolio performance summary."""
        winning_trades = [t for t in self.trades if t.is_profitable]
        losing_trades = [t for t in self.trades if not t.is_profitable]
        
        return {
            'initial_cash': self.initial_cash,
            'current_value': self.total_value,
            'cash': self.cash,
            'positions_value': self.positions_value,
            'total_return': self.total_return,
            'total_pnl': self.total_pnl,
            'realized_pnl': self.realized_pnl,
            'unrealized_pnl': self.unrealized_pnl,
            'total_trades': len(self.trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': (len(winning_trades) / len(self.trades)) * 100 if self.trades else 0,
            'total_commission': self.total_commission_paid,
            'max_drawdown': self.max_drawdown,
            'peak_value': self.peak_value,
            'open_positions': len(self.positions),
            'pending_orders': len(self.order_manager.get_pending_orders())
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert portfolio to dictionary."""
        return {
            'cash': self.cash,
            'total_value': self.total_value,
            'positions': {symbol: pos.to_dict() for symbol, pos in self.positions.items()},
            'pending_orders': [order.to_dict() for order in self.order_manager.get_pending_orders()],
            'recent_trades': [trade.__dict__ for trade in self.trades[-10:]],  # Last 10 trades
            'performance': self.get_performance_summary()
        }
