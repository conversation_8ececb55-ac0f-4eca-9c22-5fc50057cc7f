"""
Market data API endpoints.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api.dependencies import (
    get_data_service,
    get_market_data_service,
    validate_symbol,
    validate_timeframe,
    validate_pagination
)
from app.api.schemas.market_data import (
    MarketDataResponse,
    SymbolListResponse,
    QuoteResponse,
    MultiQuoteResponse,
    SymbolStatsResponse,
    TimeframeListResponse,
    MarketStatusResponse
)
from app.api.schemas.common import OHLCVData, SymbolInfo, QuoteData
from app.services.data_service import DataService
from app.services.market_data_service import MarketDataService
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/symbols", response_model=SymbolListResponse)
async def get_symbols(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    market_type: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    data_service: DataService = Depends(get_data_service)
):
    """Get list of available symbols with pagination and filtering."""
    try:
        skip, limit = validate_pagination(skip, limit)
        
        # Get symbols with filters
        symbols = data_service.get_symbols(
            skip=skip,
            limit=limit,
            market_type=market_type,
            search=search
        )
        
        # Get total count
        total = data_service.count_symbols(
            market_type=market_type,
            search=search
        )
        
        # Convert to response format
        symbol_list = [
            SymbolInfo(
                id=symbol.id,
                symbol=symbol.symbol,
                name=symbol.name,
                market_type=symbol.market_type.value,
                exchange=symbol.exchange,
                is_active=symbol.is_active
            )
            for symbol in symbols
        ]
        
        return SymbolListResponse(
            symbols=symbol_list,
            total=total,
            skip=skip,
            limit=limit,
            has_more=(skip + limit) < total
        )
        
    except Exception as e:
        logger.error(f"Error getting symbols: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve symbols"
        )


@router.get("/ohlcv/{symbol}", response_model=MarketDataResponse)
async def get_ohlcv_data(
    symbol: str,
    timeframe: str = Query("1m"),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    limit: Optional[int] = Query(1000, ge=1, le=10000),
    data_service: DataService = Depends(get_data_service)
):
    """Get OHLCV data for a symbol."""
    try:
        symbol = validate_symbol(symbol)
        timeframe = validate_timeframe(timeframe)
        
        # Set default date range if not provided
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        # Get data based on timeframe
        if timeframe == "1m":
            # Get 1-minute data
            ohlcv_data = data_service.get_ohlcv_data(
                symbol=symbol,
                start_time=start_date,
                end_time=end_date,
                as_dataframe=False
            )
        else:
            # Get aggregated data
            ohlcv_data = data_service.get_aggregated_data(
                symbol=symbol,
                timeframe=timeframe,
                start_time=start_date,
                end_time=end_date,
                limit=limit
            )
        
        if not ohlcv_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No data found for symbol {symbol}"
            )
        
        # Apply limit if specified
        if limit and len(ohlcv_data) > limit:
            ohlcv_data = ohlcv_data[-limit:]
        
        # Convert to response format
        data_list = [
            OHLCVData(
                timestamp=record.timestamp,
                open=float(record.open),
                high=float(record.high),
                low=float(record.low),
                close=float(record.close),
                volume=record.volume
            )
            for record in ohlcv_data
        ]
        
        return MarketDataResponse(
            symbol=symbol,
            timeframe=timeframe,
            data=data_list,
            total_records=len(data_list),
            start_date=start_date,
            end_date=end_date
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting OHLCV data for {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve market data"
        )


@router.get("/quote/{symbol}", response_model=QuoteResponse)
async def get_quote(
    symbol: str,
    market_data_service: MarketDataService = Depends(get_market_data_service)
):
    """Get real-time quote for a symbol."""
    try:
        symbol = validate_symbol(symbol)
        
        # Get live quote
        quote_data = market_data_service.get_live_quote(symbol)
        
        if not quote_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No quote data available for symbol {symbol}"
            )
        
        quote = QuoteData(
            symbol=symbol,
            ltp=quote_data.ltp,
            change=quote_data.change,
            change_percent=quote_data.change_percent,
            volume=quote_data.volume,
            timestamp=quote_data.timestamp
        )
        
        return QuoteResponse(quote=quote)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting quote for {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve quote data"
        )


@router.get("/quotes", response_model=MultiQuoteResponse)
async def get_multiple_quotes(
    symbols: str = Query(..., description="Comma-separated list of symbols"),
    market_data_service: MarketDataService = Depends(get_market_data_service)
):
    """Get real-time quotes for multiple symbols."""
    try:
        symbol_list = [validate_symbol(s.strip()) for s in symbols.split(",")]
        
        if len(symbol_list) > 50:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 50 symbols allowed per request"
            )
        
        quotes = {}
        for symbol in symbol_list:
            quote_data = market_data_service.get_live_quote(symbol)
            if quote_data:
                quotes[symbol] = QuoteData(
                    symbol=symbol,
                    ltp=quote_data.ltp,
                    change=quote_data.change,
                    change_percent=quote_data.change_percent,
                    volume=quote_data.volume,
                    timestamp=quote_data.timestamp
                )
        
        return MultiQuoteResponse(
            quotes=quotes,
            timestamp=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting multiple quotes: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve quote data"
        )


@router.get("/stats/{symbol}", response_model=SymbolStatsResponse)
async def get_symbol_stats(
    symbol: str,
    data_service: DataService = Depends(get_data_service)
):
    """Get statistics for a symbol."""
    try:
        symbol = validate_symbol(symbol)
        
        stats = data_service.get_symbol_statistics(symbol)
        
        if not stats:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No statistics available for symbol {symbol}"
            )
        
        return SymbolStatsResponse(
            symbol=symbol,
            stats=stats
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stats for {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve symbol statistics"
        )


@router.get("/timeframes", response_model=TimeframeListResponse)
async def get_timeframes():
    """Get list of available timeframes."""
    return TimeframeListResponse()


@router.get("/market-status", response_model=MarketStatusResponse)
async def get_market_status():
    """Get current market status."""
    try:
        # Simple market hours check (9:15 AM to 3:30 PM IST on weekdays)
        now = datetime.utcnow()
        # Convert to IST (UTC+5:30)
        ist_now = now + timedelta(hours=5, minutes=30)
        
        is_weekday = ist_now.weekday() < 5  # Monday = 0, Sunday = 6
        market_start = ist_now.replace(hour=9, minute=15, second=0, microsecond=0)
        market_end = ist_now.replace(hour=15, minute=30, second=0, microsecond=0)
        
        is_open = is_weekday and market_start <= ist_now <= market_end
        
        # Calculate next open/close times
        next_open = None
        next_close = None
        
        if is_open:
            next_close = market_end
        else:
            if ist_now < market_start and is_weekday:
                next_open = market_start
            else:
                # Next business day
                days_ahead = 1
                if ist_now.weekday() >= 4:  # Friday or later
                    days_ahead = 7 - ist_now.weekday()
                next_open = (ist_now + timedelta(days=days_ahead)).replace(
                    hour=9, minute=15, second=0, microsecond=0
                )
        
        return MarketStatusResponse(
            is_open=is_open,
            next_open=next_open,
            next_close=next_close
        )
        
    except Exception as e:
        logger.error(f"Error getting market status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve market status"
        )
