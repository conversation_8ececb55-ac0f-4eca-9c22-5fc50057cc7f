"""
Data ingestion service for fetching and storing historical market data.
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta, time
import pandas as pd
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
import asyncio
import time as time_module

from app.core.logging import get_logger
from app.database.models import Symbol, StockOHLCV, MarketType
from app.database.repositories.symbol_repository import SymbolRepository
from app.database.repositories.ohlcv_repository import OHLCVRepository
from app.integrations.fyers_client import FyersClient
from app.core.config import get_settings

logger = get_logger(__name__)
settings = get_settings()


class DataIngestionService:
    """Service for ingesting historical market data."""
    
    def __init__(self, db: Session):
        """
        Initialize data ingestion service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.symbol_repo = SymbolRepository(db)
        self.ohlcv_repo = OHLCVRepository(db)
        self.fyers_client = FyersClient()
        
        # Ingestion configuration
        self.batch_size = 1000
        self.rate_limit_delay = 1.0  # seconds between API calls
        self.max_retries = 3
        self.retry_delay = 5.0  # seconds
    
    async def ingest_symbol_list(self, market_types: List[str] = None) -> int:
        """
        Ingest symbol list from Fyers API.
        
        Args:
            market_types: List of market types to ingest
            
        Returns:
            Number of symbols ingested
        """
        try:
            logger.info("Starting symbol list ingestion...")
            
            if not self.fyers_client.is_authenticated():
                logger.error("Fyers client not authenticated")
                return 0
            
            # Get symbol list from Fyers
            symbols_data = self.fyers_client.get_symbol_list()
            
            if not symbols_data:
                logger.error("No symbols data received from Fyers")
                return 0
            
            ingested_count = 0
            
            for symbol_info in symbols_data:
                try:
                    # Extract symbol information
                    symbol_name = symbol_info.get('symbol', '').strip()
                    name = symbol_info.get('name', '').strip()
                    exchange = symbol_info.get('exchange', '').strip()
                    market_type_str = symbol_info.get('market_type', 'EQUITY').upper()
                    
                    if not symbol_name:
                        continue
                    
                    # Convert market type
                    try:
                        market_type = MarketType(market_type_str)
                    except ValueError:
                        market_type = MarketType.EQUITY
                    
                    # Filter by market types if specified
                    if market_types and market_type.value not in market_types:
                        continue
                    
                    # Check if symbol already exists
                    existing_symbol = self.symbol_repo.get_by_symbol(symbol_name)
                    
                    if existing_symbol:
                        # Update existing symbol
                        existing_symbol.name = name
                        existing_symbol.exchange = exchange
                        existing_symbol.market_type = market_type
                        existing_symbol.is_active = True
                        existing_symbol.updated_at = datetime.utcnow()
                    else:
                        # Create new symbol
                        new_symbol = Symbol(
                            symbol=symbol_name,
                            name=name,
                            exchange=exchange,
                            market_type=market_type,
                            is_active=True,
                            created_at=datetime.utcnow(),
                            updated_at=datetime.utcnow()
                        )
                        self.db.add(new_symbol)
                    
                    ingested_count += 1
                    
                    # Commit in batches
                    if ingested_count % self.batch_size == 0:
                        self.db.commit()
                        logger.info(f"Ingested {ingested_count} symbols...")
                        
                except Exception as e:
                    logger.error(f"Error processing symbol {symbol_info}: {e}")
                    continue
            
            # Final commit
            self.db.commit()
            
            logger.info(f"Symbol ingestion completed: {ingested_count} symbols")
            return ingested_count
            
        except Exception as e:
            logger.error(f"Error in symbol list ingestion: {e}")
            self.db.rollback()
            return 0
    
    async def ingest_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str = "1",
        force_update: bool = False
    ) -> int:
        """
        Ingest historical OHLCV data for a symbol.
        
        Args:
            symbol: Symbol to ingest
            start_date: Start date
            end_date: End date
            timeframe: Timeframe (1, 5, 15, 30, 60, D)
            force_update: Whether to force update existing data
            
        Returns:
            Number of records ingested
        """
        try:
            logger.info(f"Starting historical data ingestion for {symbol} ({timeframe})")
            
            # Get symbol from database
            symbol_obj = self.symbol_repo.get_by_symbol(symbol)
            if not symbol_obj:
                logger.error(f"Symbol not found in database: {symbol}")
                return 0
            
            # Check if data already exists
            if not force_update:
                existing_data = self.ohlcv_repo.get_data_range(
                    symbol_obj.id, start_date, end_date
                )
                if existing_data:
                    logger.info(f"Data already exists for {symbol}, skipping...")
                    return 0
            
            # Authenticate Fyers client if needed
            if not self.fyers_client.is_authenticated():
                logger.error("Fyers client not authenticated")
                return 0
            
            # Fetch data from Fyers API
            historical_data = await self._fetch_historical_data_with_retry(
                symbol, start_date, end_date, timeframe
            )
            
            if not historical_data:
                logger.warning(f"No historical data received for {symbol}")
                return 0
            
            # Process and store data
            ingested_count = await self._store_ohlcv_data(
                symbol_obj.id, historical_data, force_update
            )
            
            logger.info(f"Historical data ingestion completed for {symbol}: {ingested_count} records")
            return ingested_count
            
        except Exception as e:
            logger.error(f"Error in historical data ingestion for {symbol}: {e}")
            self.db.rollback()
            return 0
    
    async def _fetch_historical_data_with_retry(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str
    ) -> Optional[List[Dict[str, Any]]]:
        """Fetch historical data with retry logic."""
        
        for attempt in range(self.max_retries):
            try:
                # Rate limiting
                await asyncio.sleep(self.rate_limit_delay)
                
                # Fetch data from Fyers
                data = self.fyers_client.get_historical_data(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    timeframe=timeframe
                )
                
                if data:
                    return data
                
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {e}")
                
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                else:
                    logger.error(f"All retry attempts failed for {symbol}")
        
        return None
    
    async def _store_ohlcv_data(
        self,
        symbol_id: int,
        data: List[Dict[str, Any]],
        force_update: bool = False
    ) -> int:
        """Store OHLCV data in database."""
        
        try:
            ingested_count = 0
            
            for record in data:
                try:
                    # Extract data fields
                    timestamp = record.get('timestamp')
                    if isinstance(timestamp, str):
                        timestamp = datetime.fromisoformat(timestamp)
                    elif isinstance(timestamp, (int, float)):
                        timestamp = datetime.fromtimestamp(timestamp)
                    
                    open_price = float(record.get('open', 0))
                    high_price = float(record.get('high', 0))
                    low_price = float(record.get('low', 0))
                    close_price = float(record.get('close', 0))
                    volume = int(record.get('volume', 0))
                    
                    # Validate data
                    if not self._validate_ohlcv_record(
                        timestamp, open_price, high_price, low_price, close_price, volume
                    ):
                        continue
                    
                    # Check if record already exists
                    existing_record = self.ohlcv_repo.get_by_symbol_and_time(
                        symbol_id, timestamp
                    )
                    
                    if existing_record:
                        if force_update:
                            # Update existing record
                            existing_record.open = open_price
                            existing_record.high = high_price
                            existing_record.low = low_price
                            existing_record.close = close_price
                            existing_record.volume = volume
                            existing_record.updated_at = datetime.utcnow()
                        else:
                            # Skip existing record
                            continue
                    else:
                        # Create new record
                        new_record = StockOHLCV(
                            symbol_id=symbol_id,
                            timestamp=timestamp,
                            open=open_price,
                            high=high_price,
                            low=low_price,
                            close=close_price,
                            volume=volume,
                            created_at=datetime.utcnow(),
                            updated_at=datetime.utcnow()
                        )
                        self.db.add(new_record)
                    
                    ingested_count += 1
                    
                    # Commit in batches
                    if ingested_count % self.batch_size == 0:
                        self.db.commit()
                        
                except Exception as e:
                    logger.error(f"Error processing OHLCV record: {e}")
                    continue
            
            # Final commit
            self.db.commit()
            return ingested_count
            
        except Exception as e:
            logger.error(f"Error storing OHLCV data: {e}")
            self.db.rollback()
            return 0
    
    def _validate_ohlcv_record(
        self,
        timestamp: datetime,
        open_price: float,
        high_price: float,
        low_price: float,
        close_price: float,
        volume: int
    ) -> bool:
        """Validate OHLCV record."""
        
        # Check timestamp
        if not timestamp:
            return False
        
        # Check prices are positive
        if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
            return False
        
        # Check high >= low
        if high_price < low_price:
            return False
        
        # Check open and close are within high-low range
        if not (low_price <= open_price <= high_price):
            return False
        
        if not (low_price <= close_price <= high_price):
            return False
        
        # Check volume is non-negative
        if volume < 0:
            return False
        
        return True
    
    async def bulk_ingest_historical_data(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        timeframe: str = "1",
        max_concurrent: int = 5
    ) -> Dict[str, int]:
        """
        Bulk ingest historical data for multiple symbols.
        
        Args:
            symbols: List of symbols to ingest
            start_date: Start date
            end_date: End date
            timeframe: Timeframe
            max_concurrent: Maximum concurrent requests
            
        Returns:
            Dictionary with symbol -> records_ingested mapping
        """
        
        logger.info(f"Starting bulk ingestion for {len(symbols)} symbols")
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def ingest_single_symbol(symbol: str) -> Tuple[str, int]:
            async with semaphore:
                count = await self.ingest_historical_data(
                    symbol, start_date, end_date, timeframe
                )
                return symbol, count
        
        # Execute ingestion tasks
        tasks = [ingest_single_symbol(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        ingestion_results = {}
        total_records = 0
        
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"Bulk ingestion error: {result}")
                continue
            
            symbol, count = result
            ingestion_results[symbol] = count
            total_records += count
        
        logger.info(f"Bulk ingestion completed: {total_records} total records")
        return ingestion_results
    
    def get_ingestion_status(self, symbol: str = None) -> Dict[str, Any]:
        """Get data ingestion status."""
        
        try:
            status = {
                'total_symbols': self.symbol_repo.count(),
                'active_symbols': self.symbol_repo.count({'is_active': True}),
                'total_ohlcv_records': self.ohlcv_repo.count(),
                'last_update': None,
                'data_coverage': {}
            }
            
            # Get last update timestamp
            latest_record = self.db.query(func.max(OHLCVData.updated_at)).scalar()
            if latest_record:
                status['last_update'] = latest_record.isoformat()
            
            # Get data coverage by symbol
            if symbol:
                symbol_obj = self.symbol_repo.get_by_symbol(symbol)
                if symbol_obj:
                    coverage = self.ohlcv_repo.get_data_coverage(symbol_obj.id)
                    status['data_coverage'][symbol] = coverage
            else:
                # Get coverage for top 10 symbols by data volume
                top_symbols = self.db.query(
                    Symbol.symbol,
                    func.count(OHLCVData.id).label('record_count')
                ).join(OHLCVData).group_by(Symbol.symbol).order_by(
                    func.count(OHLCVData.id).desc()
                ).limit(10).all()
                
                for symbol_name, record_count in top_symbols:
                    symbol_obj = self.symbol_repo.get_by_symbol(symbol_name)
                    if symbol_obj:
                        coverage = self.ohlcv_repo.get_data_coverage(symbol_obj.id)
                        coverage['record_count'] = record_count
                        status['data_coverage'][symbol_name] = coverage
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting ingestion status: {e}")
            return {'error': str(e)}
