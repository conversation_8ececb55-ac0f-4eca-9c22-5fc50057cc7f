2025-07-13 04:13:21,178 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.177208Z"}
2025-07-13 04:13:21,178 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.178217Z"}
2025-07-13 04:13:21,179 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.179218Z"}
2025-07-13 04:13:21,180 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.180227Z"}
2025-07-13 04:13:21,386 [INFO] app.database.connection: Database connection successful
2025-07-13 04:13:21,387 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.387353Z"}
2025-07-13 04:13:21,387 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:43:21.387353Z"}
2025-07-13 04:13:21,388 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:13:21,388 [INFO] app.database.connection: Initializing database...
2025-07-13 04:13:21,392 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:13:21,679 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:13:21,679 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:13:21,709 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:13:21,710 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:13:21,711 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'stock_ohlcv', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:43:21.711311Z"}
2025-07-13 04:14:15,839 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.839084Z"}
2025-07-13 04:14:15,840 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.840051Z"}
2025-07-13 04:14:15,841 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.841048Z"}
2025-07-13 04:14:15,842 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:15.842058Z"}
2025-07-13 04:14:16,028 [INFO] app.database.connection: Database connection successful
2025-07-13 04:14:16,028 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:16.028458Z"}
2025-07-13 04:14:16,029 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:16.029458Z"}
2025-07-13 04:14:16,030 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:14:16,030 [INFO] app.database.connection: Initializing database...
2025-07-13 04:14:16,033 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:14:16,061 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:14:16,062 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:14:16,068 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:16,069 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'stock_ohlcv', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:16,070 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'stock_ohlcv', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:44:16.070339Z"}
2025-07-13 04:14:44,669 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.669528Z"}
2025-07-13 04:14:44,670 [INFO] signal_stack: {"event": "DATABASE RESET", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.670526Z"}
2025-07-13 04:14:44,670 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.670526Z"}
2025-07-13 04:14:44,671 [INFO] signal_stack: {"event": "Dropping all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.671525Z"}
2025-07-13 04:14:44,863 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.863536Z"}
2025-07-13 04:14:44,868 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv_agg", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.868536Z"}
2025-07-13 04:14:44,873 [INFO] signal_stack: {"event": "Dropped table: screener_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.872545Z"}
2025-07-13 04:14:44,877 [INFO] signal_stack: {"event": "Dropped table: paper_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.877533Z"}
2025-07-13 04:14:44,881 [INFO] signal_stack: {"event": "Dropped table: backtest_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.881535Z"}
2025-07-13 04:14:44,885 [INFO] signal_stack: {"event": "Dropped table: backtest_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.885549Z"}
2025-07-13 04:14:44,888 [INFO] signal_stack: {"event": "Dropped table: strategies", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.888542Z"}
2025-07-13 04:14:44,891 [INFO] signal_stack: {"event": "Dropped table: symbols", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.891536Z"}
2025-07-13 04:14:44,920 [INFO] signal_stack: {"event": "All tables dropped successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.920101Z"}
2025-07-13 04:14:44,921 [INFO] signal_stack: {"event": "Recreating all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:44.921111Z"}
2025-07-13 04:14:45,148 [INFO] signal_stack: {"event": "All tables recreated successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:45.148443Z"}
2025-07-13 04:14:45,149 [INFO] signal_stack: {"event": "Database reset completed successfully!", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:45.149440Z"}
2025-07-13 04:14:55,569 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.569862Z"}
2025-07-13 04:14:55,570 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.570829Z"}
2025-07-13 04:14:55,571 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.571826Z"}
2025-07-13 04:14:55,571 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.571826Z"}
2025-07-13 04:14:55,767 [INFO] app.database.connection: Database connection successful
2025-07-13 04:14:55,768 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.768055Z"}
2025-07-13 04:14:55,769 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:44:55.768055Z"}
2025-07-13 04:14:55,769 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:14:55,769 [INFO] app.database.connection: Initializing database...
2025-07-13 04:14:55,773 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:14:55,797 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:14:55,797 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:14:55,823 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:55,824 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:14:55,825 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'screener_results', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:44:55.825706Z"}
2025-07-13 04:15:42,336 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.336996Z"}
2025-07-13 04:15:42,338 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.338288Z"}
2025-07-13 04:15:42,338 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.338288Z"}
2025-07-13 04:15:42,339 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.339279Z"}
2025-07-13 04:15:42,530 [INFO] app.database.connection: Database connection successful
2025-07-13 04:15:42,530 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.530331Z"}
2025-07-13 04:15:42,531 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:45:42.531333Z"}
2025-07-13 04:15:42,531 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:15:42,531 [INFO] app.database.connection: Initializing database...
2025-07-13 04:15:42,535 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:15:42,562 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:15:42,563 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:15:42,592 [ERROR] app.database.init_db: Failed to create hypertables: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:15:42,593 [ERROR] app.database.init_db: Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column "timestamp" (used in partitioning)
HINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.

[SQL: 
                SELECT create_hypertable(
                    'screener_results', 
                    'timestamp',
                    if_not_exists => TRUE,
                    chunk_time_interval => INTERVAL '1 day'
                );
            ]
(Background on this error at: https://sqlalche.me/e/20/4xp6)
2025-07-13 04:15:42,593 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.DatabaseError) cannot create a unique index without the column \"timestamp\" (used in partitioning)\nHINT:  If you're creating a hypertable on a table with a primary key, ensure the partitioning column is part of the primary or composite key.\n\n[SQL: \n                SELECT create_hypertable(\n                    'screener_results', \n                    'timestamp',\n                    if_not_exists => TRUE,\n                    chunk_time_interval => INTERVAL '1 day'\n                );\n            ]\n(Background on this error at: https://sqlalche.me/e/20/4xp6)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:45:42.593657Z"}
2025-07-13 04:18:44,872 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:44.872548Z"}
2025-07-13 04:18:44,873 [INFO] signal_stack: {"event": "DATABASE RESET", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:44.873530Z"}
2025-07-13 04:18:44,873 [INFO] signal_stack: {"event": "============================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:44.873530Z"}
2025-07-13 04:18:44,874 [INFO] signal_stack: {"event": "Dropping all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:44.874925Z"}
2025-07-13 04:18:45,073 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.073546Z"}
2025-07-13 04:18:45,077 [INFO] signal_stack: {"event": "Dropped table: stock_ohlcv_agg", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.077519Z"}
2025-07-13 04:18:45,082 [INFO] signal_stack: {"event": "Dropped table: screener_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.081917Z"}
2025-07-13 04:18:45,086 [INFO] signal_stack: {"event": "Dropped table: paper_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.086914Z"}
2025-07-13 04:18:45,090 [INFO] signal_stack: {"event": "Dropped table: backtest_trades", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.090920Z"}
2025-07-13 04:18:45,094 [INFO] signal_stack: {"event": "Dropped table: backtest_results", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.094913Z"}
2025-07-13 04:18:45,098 [INFO] signal_stack: {"event": "Dropped table: strategies", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.097934Z"}
2025-07-13 04:18:45,100 [INFO] signal_stack: {"event": "Dropped table: symbols", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.100934Z"}
2025-07-13 04:18:45,139 [INFO] signal_stack: {"event": "All tables dropped successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.138165Z"}
2025-07-13 04:18:45,139 [INFO] signal_stack: {"event": "Recreating all tables...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.139161Z"}
2025-07-13 04:18:45,343 [INFO] signal_stack: {"event": "All tables recreated successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.343078Z"}
2025-07-13 04:18:45,343 [INFO] signal_stack: {"event": "Database reset completed successfully!", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:45.343078Z"}
2025-07-13 04:18:55,806 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:55.806433Z"}
2025-07-13 04:18:55,807 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:55.807433Z"}
2025-07-13 04:18:55,807 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:55.807433Z"}
2025-07-13 04:18:55,808 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:55.808433Z"}
2025-07-13 04:18:56,001 [INFO] app.database.connection: Database connection successful
2025-07-13 04:18:56,001 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:56.001413Z"}
2025-07-13 04:18:56,002 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:48:56.002426Z"}
2025-07-13 04:18:56,002 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:18:56,003 [INFO] app.database.connection: Initializing database...
2025-07-13 04:18:56,006 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:18:56,032 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:18:56,032 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:18:56,089 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:18:56,090 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:18:56,114 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:18:56,114 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:18:56,129 [ERROR] app.database.init_db: Failed to create stored procedures: (psycopg2.errors.SyntaxError) syntax error at or near "timestamp"
LINE 8:                 ) RETURNS TABLE(timestamp TIMESTAMP, sma NUM...
                                        ^

[SQL: 
                CREATE OR REPLACE FUNCTION calculate_sma(
                    p_symbol_id INTEGER,
                    p_timeframe VARCHAR(10),
                    p_period INTEGER,
                    p_start_time TIMESTAMP,
                    p_end_time TIMESTAMP
                ) RETURNS TABLE(timestamp TIMESTAMP, sma NUMERIC) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        t.timestamp,
                        AVG(t.close) OVER (
                            ORDER BY t.timestamp 
                            ROWS BETWEEN p_period-1 PRECEDING AND CURRENT ROW
                        ) as sma
                    FROM stock_ohlcv_agg t
                    WHERE t.symbol_id = p_symbol_id
                        AND t.timeframe = p_timeframe
                        AND t.timestamp >= p_start_time
                        AND t.timestamp <= p_end_time
                    ORDER BY t.timestamp;
                END;
                $$ LANGUAGE plpgsql;
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-13 04:18:56,130 [ERROR] app.database.init_db: Database setup failed: (psycopg2.errors.SyntaxError) syntax error at or near "timestamp"
LINE 8:                 ) RETURNS TABLE(timestamp TIMESTAMP, sma NUM...
                                        ^

[SQL: 
                CREATE OR REPLACE FUNCTION calculate_sma(
                    p_symbol_id INTEGER,
                    p_timeframe VARCHAR(10),
                    p_period INTEGER,
                    p_start_time TIMESTAMP,
                    p_end_time TIMESTAMP
                ) RETURNS TABLE(timestamp TIMESTAMP, sma NUMERIC) AS $$
                BEGIN
                    RETURN QUERY
                    SELECT 
                        t.timestamp,
                        AVG(t.close) OVER (
                            ORDER BY t.timestamp 
                            ROWS BETWEEN p_period-1 PRECEDING AND CURRENT ROW
                        ) as sma
                    FROM stock_ohlcv_agg t
                    WHERE t.symbol_id = p_symbol_id
                        AND t.timeframe = p_timeframe
                        AND t.timestamp >= p_start_time
                        AND t.timestamp <= p_end_time
                    ORDER BY t.timestamp;
                END;
                $$ LANGUAGE plpgsql;
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-13 04:18:56,132 [ERROR] signal_stack: {"event": "Database setup failed: (psycopg2.errors.SyntaxError) syntax error at or near \"timestamp\"\nLINE 8:                 ) RETURNS TABLE(timestamp TIMESTAMP, sma NUM...\n                                        ^\n\n[SQL: \n                CREATE OR REPLACE FUNCTION calculate_sma(\n                    p_symbol_id INTEGER,\n                    p_timeframe VARCHAR(10),\n                    p_period INTEGER,\n                    p_start_time TIMESTAMP,\n                    p_end_time TIMESTAMP\n                ) RETURNS TABLE(timestamp TIMESTAMP, sma NUMERIC) AS $$\n                BEGIN\n                    RETURN QUERY\n                    SELECT \n                        t.timestamp,\n                        AVG(t.close) OVER (\n                            ORDER BY t.timestamp \n                            ROWS BETWEEN p_period-1 PRECEDING AND CURRENT ROW\n                        ) as sma\n                    FROM stock_ohlcv_agg t\n                    WHERE t.symbol_id = p_symbol_id\n                        AND t.timeframe = p_timeframe\n                        AND t.timestamp >= p_start_time\n                        AND t.timestamp <= p_end_time\n                    ORDER BY t.timestamp;\n                END;\n                $$ LANGUAGE plpgsql;\n            ]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:48:56.132130Z"}
2025-07-13 04:19:24,086 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.086659Z"}
2025-07-13 04:19:24,087 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.087645Z"}
2025-07-13 04:19:24,087 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.087645Z"}
2025-07-13 04:19:24,088 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.088654Z"}
2025-07-13 04:19:24,273 [INFO] app.database.connection: Database connection successful
2025-07-13 04:19:24,274 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.274825Z"}
2025-07-13 04:19:24,274 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.274825Z"}
2025-07-13 04:19:24,274 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:19:24,275 [INFO] app.database.connection: Initializing database...
2025-07-13 04:19:24,278 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:19:24,304 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:19:24,305 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:19:24,313 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:19:24,314 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:19:24,318 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:19:24,319 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:19:24,327 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:19:24,328 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:19:24,328 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.328204Z"}
2025-07-13 04:19:24,329 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.329200Z"}
2025-07-13 04:19:24,330 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.330192Z"}
2025-07-13 04:19:24,381 [INFO] signal_stack: {"event": "Creating NIFTY symbol in database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.381066Z"}
2025-07-13 04:19:24,382 [INFO] app.services.data_service: {"event": "Creating symbol: NIFTY", "logger": "app.services.data_service", "level": "info", "timestamp": "2025-07-12T22:49:24.382071Z"}
2025-07-13 04:19:24,396 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol created successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.396381Z"}
2025-07-13 04:19:24,397 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.397376Z"}
2025-07-13 04:19:24,398 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:49:24.398379Z"}
2025-07-13 04:19:24,409 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T22:49:24.409655Z"}
2025-07-13 04:19:24,410 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:49:24.410656Z"}
2025-07-13 04:19:25,431 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:49:25.431944Z"}
2025-07-13 04:20:13,466 [ERROR] app.integrations.fyers.auth: {"event": "Token generation failed: {'code': -5, 'message': 'invalid app id hash', 's': 'error'}", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T22:50:13.466548Z"}
2025-07-13 04:20:13,467 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T22:50:13.467553Z"}
2025-07-13 04:20:13,467 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T22:50:13.467553Z"}
2025-07-13 04:20:13,468 [ERROR] signal_stack: {"event": "Failed to initialize Fyers connection", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:50:13.468552Z"}
2025-07-13 04:21:21,513 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.513032Z"}
2025-07-13 04:21:21,513 [INFO] signal_stack: {"event": "NIFTY PIPELINE TEST SETUP (WITHOUT FYERS)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.513032Z"}
2025-07-13 04:21:21,514 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.514036Z"}
2025-07-13 04:21:21,514 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.514036Z"}
2025-07-13 04:21:21,698 [INFO] app.database.connection: Database connection successful
2025-07-13 04:21:21,699 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.699575Z"}
2025-07-13 04:21:21,700 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.699575Z"}
2025-07-13 04:21:21,700 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:21:21,700 [INFO] app.database.connection: Initializing database...
2025-07-13 04:21:21,704 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:21:21,729 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:21:21,730 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:21:21,738 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:21:21,739 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:21:21,744 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:21:21,745 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:21:21,753 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:21:21,753 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:21:21,754 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.754556Z"}
2025-07-13 04:21:21,755 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.755569Z"}
2025-07-13 04:21:21,756 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.755569Z"}
2025-07-13 04:21:21,807 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.807933Z"}
2025-07-13 04:21:21,809 [INFO] signal_stack: {"event": "\nStep 4: Creating sample data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.809935Z"}
2025-07-13 04:21:21,809 [INFO] signal_stack: {"event": "Creating sample OHLCV data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:51:21.809935Z"}
2025-07-13 04:21:21,847 [ERROR] signal_stack: {"event": "Error creating sample data: DataService.store_ohlcv_data() missing 1 required positional argument: 'ohlcv_data'", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:51:21.847081Z"}
2025-07-13 04:22:15,002 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.002476Z"}
2025-07-13 04:22:15,003 [INFO] signal_stack: {"event": "NIFTY PIPELINE TEST SETUP (WITHOUT FYERS)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.003472Z"}
2025-07-13 04:22:15,004 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.004476Z"}
2025-07-13 04:22:15,004 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.004476Z"}
2025-07-13 04:22:15,217 [INFO] app.database.connection: Database connection successful
2025-07-13 04:22:15,217 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.217048Z"}
2025-07-13 04:22:15,218 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.218047Z"}
2025-07-13 04:22:15,219 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:22:15,219 [INFO] app.database.connection: Initializing database...
2025-07-13 04:22:15,222 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:22:15,255 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:22:15,255 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:22:15,264 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:22:15,265 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:22:15,270 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:22:15,270 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:22:15,279 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:22:15,280 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:22:15,280 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.280750Z"}
2025-07-13 04:22:15,280 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.280750Z"}
2025-07-13 04:22:15,281 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.281746Z"}
2025-07-13 04:22:15,337 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.337051Z"}
2025-07-13 04:22:15,338 [INFO] signal_stack: {"event": "\nStep 4: Creating sample data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.338061Z"}
2025-07-13 04:22:15,339 [INFO] signal_stack: {"event": "Creating sample OHLCV data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.339474Z"}
2025-07-13 04:22:15,597 [INFO] app.services.data_service: {"event": "Stored 782 OHLCV records for NIFTY", "logger": "app.services.data_service", "level": "info", "timestamp": "2025-07-12T22:52:15.596934Z"}
2025-07-13 04:22:15,597 [INFO] signal_stack: {"event": "\u2713 Created 782 sample OHLCV records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.597935Z"}
2025-07-13 04:22:15,598 [INFO] signal_stack: {"event": "\nStep 5: Testing timeframe aggregation...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.598936Z"}
2025-07-13 04:22:15,599 [INFO] signal_stack: {"event": "Testing timeframe aggregation...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.599935Z"}
2025-07-13 04:22:15,600 [INFO] signal_stack: {"event": "Aggregating NIFTY data for 5m...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.600938Z"}
2025-07-13 04:22:15,608 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:22:15.600938 to 2025-07-13 04:22:15.600938", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.607280Z"}
2025-07-13 04:22:15,662 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.661120Z"}
2025-07-13 04:22:15,662 [INFO] signal_stack: {"event": "\u2713 5m aggregation successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.662121Z"}
2025-07-13 04:22:15,663 [INFO] signal_stack: {"event": "Aggregating NIFTY data for 15m...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.663103Z"}
2025-07-13 04:22:15,667 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:22:15.663103 to 2025-07-13 04:22:15.663103", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.667130Z"}
2025-07-13 04:22:15,677 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.677488Z"}
2025-07-13 04:22:15,678 [INFO] signal_stack: {"event": "\u2713 15m aggregation successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.678522Z"}
2025-07-13 04:22:15,679 [INFO] signal_stack: {"event": "Aggregating NIFTY data for 30m...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.678522Z"}
2025-07-13 04:22:15,682 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:22:15.679489 to 2025-07-13 04:22:15.679489", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.682848Z"}
2025-07-13 04:22:15,689 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.689830Z"}
2025-07-13 04:22:15,690 [INFO] signal_stack: {"event": "\u2713 30m aggregation successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.690832Z"}
2025-07-13 04:22:15,691 [INFO] signal_stack: {"event": "Aggregating NIFTY data for 1h...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.691838Z"}
2025-07-13 04:22:15,695 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:22:15.691838 to 2025-07-13 04:22:15.691838", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.695147Z"}
2025-07-13 04:22:15,701 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:52:15.701445Z"}
2025-07-13 04:22:15,702 [INFO] signal_stack: {"event": "\u2713 1h aggregation successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.702477Z"}
2025-07-13 04:22:15,717 [INFO] signal_stack: {"event": "Aggregation statistics:", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.717868Z"}
2025-07-13 04:22:15,718 [INFO] signal_stack: {"event": "  5m_records: 158", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.718841Z"}
2025-07-13 04:22:15,719 [INFO] signal_stack: {"event": "  10m_records: 0", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.719875Z"}
2025-07-13 04:22:15,719 [INFO] signal_stack: {"event": "  15m_records: 54", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.719875Z"}
2025-07-13 04:22:15,720 [INFO] signal_stack: {"event": "  30m_records: 28", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.719875Z"}
2025-07-13 04:22:15,720 [INFO] signal_stack: {"event": "  1h_records: 14", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.720838Z"}
2025-07-13 04:22:15,720 [INFO] signal_stack: {"event": "  2h_records: 0", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.720838Z"}
2025-07-13 04:22:15,721 [INFO] signal_stack: {"event": "  4h_records: 0", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.721841Z"}
2025-07-13 04:22:15,721 [INFO] signal_stack: {"event": "  1d_records: 0", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.721841Z"}
2025-07-13 04:22:15,723 [INFO] signal_stack: {"event": "\nStep 6: Verifying data flow...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.723210Z"}
2025-07-13 04:22:15,724 [INFO] signal_stack: {"event": "Verifying data flow...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.724214Z"}
2025-07-13 04:22:15,747 [INFO] signal_stack: {"event": "\u2713 Latest 1-minute data:", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.746017Z"}
2025-07-13 04:22:15,749 [INFO] signal_stack: {"event": "  2025-07-11 15:30:00: O=24465.9400, H=24466.9400, L=24464.6400, C=24465.1200, V=2498", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.749031Z"}
2025-07-13 04:22:15,749 [INFO] signal_stack: {"event": "  2025-07-11 15:29:00: O=24465.6100, H=24468.0100, L=24463.8100, C=24466.3300, V=3014", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.749031Z"}
2025-07-13 04:22:15,750 [INFO] signal_stack: {"event": "  2025-07-11 15:28:00: O=24469.5200, H=24473.2200, L=24464.8200, C=24466.5900, V=4462", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.750002Z"}
2025-07-13 04:22:15,750 [INFO] signal_stack: {"event": "  2025-07-11 15:27:00: O=24469.9400, H=24472.6400, L=24467.3400, C=24470.2000, V=2163", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.750002Z"}
2025-07-13 04:22:15,751 [INFO] signal_stack: {"event": "  2025-07-11 15:26:00: O=24470.4800, H=24473.8800, L=24470.4800, C=24470.6800, V=2849", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.750002Z"}
2025-07-13 04:22:15,758 [WARNING] app.services.aggregation_service: {"event": "No aggregated data found for NIFTY - 5m", "logger": "app.services.aggregation_service", "level": "warning", "timestamp": "2025-07-12T22:52:15.758373Z"}
2025-07-13 04:22:15,760 [WARNING] signal_stack: {"event": "\u26a0 No 5m aggregated data found", "logger": "signal_stack", "level": "warning", "timestamp": "2025-07-12T22:52:15.760389Z"}
2025-07-13 04:22:15,765 [WARNING] app.services.aggregation_service: {"event": "No aggregated data found for NIFTY - 15m", "logger": "app.services.aggregation_service", "level": "warning", "timestamp": "2025-07-12T22:52:15.765831Z"}
2025-07-13 04:22:15,766 [WARNING] signal_stack: {"event": "\u26a0 No 15m aggregated data found", "logger": "signal_stack", "level": "warning", "timestamp": "2025-07-12T22:52:15.766826Z"}
2025-07-13 04:22:15,771 [WARNING] app.services.aggregation_service: {"event": "No aggregated data found for NIFTY - 30m", "logger": "app.services.aggregation_service", "level": "warning", "timestamp": "2025-07-12T22:52:15.771859Z"}
2025-07-13 04:22:15,773 [WARNING] signal_stack: {"event": "\u26a0 No 30m aggregated data found", "logger": "signal_stack", "level": "warning", "timestamp": "2025-07-12T22:52:15.772860Z"}
2025-07-13 04:22:15,774 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.774847Z"}
2025-07-13 04:22:15,776 [INFO] signal_stack: {"event": "NIFTY PIPELINE TEST SETUP COMPLETED SUCCESSFULLY!", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.776155Z"}
2025-07-13 04:22:15,776 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.776155Z"}
2025-07-13 04:22:15,777 [INFO] signal_stack: {"event": "The system is now ready for:", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.777178Z"}
2025-07-13 04:22:15,777 [INFO] signal_stack: {"event": "1. Database operations with TimescaleDB", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.777178Z"}
2025-07-13 04:22:15,778 [INFO] signal_stack: {"event": "2. Automatic timeframe aggregation", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.778157Z"}
2025-07-13 04:22:15,778 [INFO] signal_stack: {"event": "3. Historical data storage and retrieval", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.778157Z"}
2025-07-13 04:22:15,779 [INFO] signal_stack: {"event": "4. Strategy development and backtesting", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.779159Z"}
2025-07-13 04:22:15,779 [INFO] signal_stack: {"event": "\nNote: Fyers API integration requires valid credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:15.779159Z"}
2025-07-13 04:22:27,258 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.258633Z"}
2025-07-13 04:22:27,259 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.259632Z"}
2025-07-13 04:22:27,260 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.260631Z"}
2025-07-13 04:22:27,261 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.260631Z"}
2025-07-13 04:22:27,261 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.261602Z"}
2025-07-13 04:22:27,262 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.262619Z"}
2025-07-13 04:22:27,262 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.262619Z"}
2025-07-13 04:22:27,459 [INFO] app.database.connection: Database connection successful
2025-07-13 04:22:27,460 [ERROR] signal_stack: {"event": "Database validation failed: Textual SQL expression 'SELECT 1 FROM pg_extensio...' should be explicitly declared as text('SELECT 1 FROM pg_extensio...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:52:27.459399Z"}
2025-07-13 04:22:27,460 [ERROR] signal_stack: {"event": "\u274c Database Connection: FAILED (0.20s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:52:27.460404Z"}
2025-07-13 04:22:27,461 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.461400Z"}
2025-07-13 04:22:27,461 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:22:27,461 [INFO] app.database.connection: Initializing database...
2025-07-13 04:22:27,465 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:22:27,492 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:22:27,492 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:22:27,501 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:22:27,501 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:22:27,506 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:22:27,507 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:22:27,516 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:22:27,517 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:22:27,517 [ERROR] signal_stack: {"event": "Schema validation failed: Textual SQL expression 'SELECT 1 FROM information...' should be explicitly declared as text('SELECT 1 FROM information...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:52:27.517078Z"}
2025-07-13 04:22:27,518 [ERROR] signal_stack: {"event": "\u274c Database Schema: FAILED (0.06s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:52:27.518077Z"}
2025-07-13 04:22:27,518 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.518077Z"}
2025-07-13 04:22:27,571 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.571295Z"}
2025-07-13 04:22:27,573 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.572295Z"}
2025-07-13 04:22:27,573 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:52:27.573296Z"}
2025-07-13 04:22:27,573 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T22:52:27.573296Z"}
2025-07-13 04:22:27,574 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:52:27.574533Z"}
2025-07-13 04:22:27,916 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:52:27.916680Z"}
2025-07-13 04:26:05,422 [ERROR] app.integrations.fyers.auth: {"event": "Token generation failed: {'code': -437, 'message': 'invalid auth code', 's': 'error'}", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T22:56:05.422674Z"}
2025-07-13 04:26:05,423 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T22:56:05.423671Z"}
2025-07-13 04:26:05,424 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T22:56:05.424676Z"}
2025-07-13 04:26:05,424 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.424676Z"}
2025-07-13 04:26:05,425 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (217.85s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.425673Z"}
2025-07-13 04:26:05,425 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.425673Z"}
2025-07-13 04:26:05,426 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T22:56:05.426669Z"}
2025-07-13 04:26:05,427 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.426669Z"}
2025-07-13 04:26:05,427 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.427672Z"}
2025-07-13 04:26:05,428 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.428671Z"}
2025-07-13 04:26:05,440 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.440253Z"}
2025-07-13 04:26:05,441 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:56:05.441274Z"}
2025-07-13 04:26:05,442 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.441274Z"}
2025-07-13 04:26:05,627 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:26:05.442258 to 2025-07-13 04:26:05.442258", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.627056Z"}
2025-07-13 04:26:05,670 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.670207Z"}
2025-07-13 04:26:05,675 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:26:05.671209 to 2025-07-13 04:26:05.671209", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.675617Z"}
2025-07-13 04:26:05,698 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.698344Z"}
2025-07-13 04:26:05,702 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:26:05.698344 to 2025-07-13 04:26:05.698344", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.702344Z"}
2025-07-13 04:26:05,732 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.732345Z"}
2025-07-13 04:26:05,736 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:26:05.733346 to 2025-07-13 04:26:05.733346", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.736353Z"}
2025-07-13 04:26:05,752 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T22:56:05.752603Z"}
2025-07-13 04:26:05,770 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.770040Z"}
2025-07-13 04:26:05,771 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.771046Z"}
2025-07-13 04:26:05,771 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.771395Z"}
2025-07-13 04:26:05,772 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.772410Z"}
2025-07-13 04:26:05,772 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.772410Z"}
2025-07-13 04:26:05,773 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.33s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.773388Z"}
2025-07-13 04:26:05,774 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:56:05.774414Z"}
2025-07-13 04:26:05,785 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T22:56:05.785393Z"}
2025-07-13 04:26:05,786 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:56:05.786389Z"}
2025-07-13 04:26:05,787 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:56:05.787391Z"}
2025-07-13 04:27:31,881 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:57:31.881051Z"}
2025-07-13 04:27:31,882 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T22:57:31.882033Z"}
2025-07-13 04:27:31,882 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T22:57:31.882033Z"}
2025-07-13 04:27:31,883 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T22:57:31.883039Z"}
2025-07-13 04:27:31,884 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T22:57:31.883039Z"}
2025-07-13 04:27:31,884 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:57:31.884055Z"}
2025-07-13 04:27:31,885 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (86.11s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:57:31.884055Z"}
2025-07-13 04:27:31,885 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.885049Z"}
2025-07-13 04:27:31,899 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.898049Z"}
2025-07-13 04:27:31,899 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.899061Z"}
2025-07-13 04:27:31,900 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.900031Z"}
2025-07-13 04:27:31,905 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.905050Z"}
2025-07-13 04:27:31,906 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.906049Z"}
2025-07-13 04:27:31,907 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.907032Z"}
2025-07-13 04:27:31,907 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.907032Z"}
2025-07-13 04:27:31,908 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.908035Z"}
2025-07-13 04:27:31,908 [INFO] signal_stack: {"event": "Overall Success Rate: 40.0% (4/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.908035Z"}
2025-07-13 04:27:31,909 [INFO] signal_stack: {"event": "\u274c FAIL Database Connection (0.20s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.909031Z"}
2025-07-13 04:27:31,909 [INFO] signal_stack: {"event": "\u274c FAIL Database Schema (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.909031Z"}
2025-07-13 04:27:31,910 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.910029Z"}
2025-07-13 04:27:31,910 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (217.85s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.910029Z"}
2025-07-13 04:27:31,910 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.910029Z"}
2025-07-13 04:27:31,911 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.911033Z"}
2025-07-13 04:27:31,911 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.33s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.911033Z"}
2025-07-13 04:27:31,912 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (86.11s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.912038Z"}
2025-07-13 04:27:31,913 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.913031Z"}
2025-07-13 04:27:31,913 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.913031Z"}
2025-07-13 04:27:31,917 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_042731.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.917030Z"}
2025-07-13 04:27:31,917 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:31.917030Z"}
2025-07-13 04:27:44,809 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:44.809788Z"}
2025-07-13 04:27:44,809 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:44.809788Z"}
2025-07-13 04:27:44,997 [INFO] app.database.connection: Database connection successful
2025-07-13 04:27:44,997 [INFO] app.database.connection: Database connection successful
2025-07-13 04:27:44,997 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:27:44,997 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:27:44,998 [INFO] app.database.connection: Initializing database...
2025-07-13 04:27:44,998 [INFO] app.database.connection: Initializing database...
2025-07-13 04:27:45,001 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:27:45,001 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:27:45,032 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:27:45,032 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:27:45,033 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:27:45,033 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:27:45,040 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:27:45,040 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:27:45,041 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:27:45,041 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:27:45,047 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:27:45,047 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:27:45,048 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:27:45,048 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:27:45,056 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:27:45,056 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:27:45,057 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:27:45,057 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:27:45,057 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:45.057606Z"}
2025-07-13 04:27:45,057 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:45.057606Z"}
2025-07-13 04:27:45,058 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:45.058607Z"}
2025-07-13 04:27:45,058 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:57:45.058607Z"}
2025-07-13 04:29:25,675 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.675533Z"}
2025-07-13 04:29:25,676 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.676559Z"}
2025-07-13 04:29:25,677 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.676559Z"}
2025-07-13 04:29:25,677 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.677539Z"}
2025-07-13 04:29:25,677 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.677539Z"}
2025-07-13 04:29:25,678 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.678564Z"}
2025-07-13 04:29:25,679 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.678564Z"}
2025-07-13 04:29:25,866 [INFO] app.database.connection: Database connection successful
2025-07-13 04:29:25,866 [ERROR] signal_stack: {"event": "Database validation failed: Textual SQL expression 'SELECT 1 FROM pg_extensio...' should be explicitly declared as text('SELECT 1 FROM pg_extensio...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:59:25.866533Z"}
2025-07-13 04:29:25,867 [ERROR] signal_stack: {"event": "\u274c Database Connection: FAILED (0.19s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:59:25.867789Z"}
2025-07-13 04:29:25,868 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.868786Z"}
2025-07-13 04:29:25,868 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:29:25,868 [INFO] app.database.connection: Initializing database...
2025-07-13 04:29:25,871 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:29:25,896 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:29:25,897 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:29:25,905 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:29:25,906 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:29:25,911 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:29:25,941 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:29:25,949 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:29:25,950 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:29:25,951 [ERROR] signal_stack: {"event": "Schema validation failed: Textual SQL expression 'SELECT 1 FROM information...' should be explicitly declared as text('SELECT 1 FROM information...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:59:25.951142Z"}
2025-07-13 04:29:25,951 [ERROR] signal_stack: {"event": "\u274c Database Schema: FAILED (0.08s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T22:59:25.951142Z"}
2025-07-13 04:29:25,952 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:25.952160Z"}
2025-07-13 04:29:26,007 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:26.006014Z"}
2025-07-13 04:29:26,008 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:26.008027Z"}
2025-07-13 04:29:26,008 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T22:59:26.008027Z"}
2025-07-13 04:29:26,009 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T22:59:26.009031Z"}
2025-07-13 04:29:26,010 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:59:26.010028Z"}
2025-07-13 04:29:26,356 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T22:59:26.356281Z"}
2025-07-13 04:32:19,499 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:02:19.499190Z"}
2025-07-13 04:32:19,500 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:02:19.499190Z"}
2025-07-13 04:32:19,500 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:02:19.500497Z"}
2025-07-13 04:32:19,500 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:02:19.500497Z"}
2025-07-13 04:32:19,501 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.501519Z"}
2025-07-13 04:32:19,501 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (173.49s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.501519Z"}
2025-07-13 04:32:19,502 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.502497Z"}
2025-07-13 04:32:19,502 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:02:19.502497Z"}
2025-07-13 04:32:19,503 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.503497Z"}
2025-07-13 04:32:19,503 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.503497Z"}
2025-07-13 04:32:19,504 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.503497Z"}
2025-07-13 04:32:19,514 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.514500Z"}
2025-07-13 04:32:19,514 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:19.514500Z"}
2025-07-13 04:32:19,515 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.515507Z"}
2025-07-13 04:32:19,683 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:32:19.516498 to 2025-07-13 04:32:19.516498", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.682038Z"}
2025-07-13 04:32:19,718 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.718740Z"}
2025-07-13 04:32:19,721 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:32:19.718740 to 2025-07-13 04:32:19.718740", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.721740Z"}
2025-07-13 04:32:19,730 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.730253Z"}
2025-07-13 04:32:19,733 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:32:19.730253 to 2025-07-13 04:32:19.730253", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.733265Z"}
2025-07-13 04:32:19,740 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.740256Z"}
2025-07-13 04:32:19,744 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:32:19.741236 to 2025-07-13 04:32:19.741236", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.744258Z"}
2025-07-13 04:32:19,751 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:02:19.751257Z"}
2025-07-13 04:32:19,766 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.766254Z"}
2025-07-13 04:32:19,766 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.766617Z"}
2025-07-13 04:32:19,766 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.766617Z"}
2025-07-13 04:32:19,767 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.767628Z"}
2025-07-13 04:32:19,767 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.767628Z"}
2025-07-13 04:32:19,769 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.768640Z"}
2025-07-13 04:32:19,769 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:19.769639Z"}
2025-07-13 04:32:19,780 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:02:19.780638Z"}
2025-07-13 04:32:19,781 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:02:19.781618Z"}
2025-07-13 04:32:19,781 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:02:19.781618Z"}
2025-07-13 04:32:29,641 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:02:29.641644Z"}
2025-07-13 04:32:29,643 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:02:29.643061Z"}
2025-07-13 04:32:29,643 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:02:29.643061Z"}
2025-07-13 04:32:29,644 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:02:29.644064Z"}
2025-07-13 04:32:29,644 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:02:29.644064Z"}
2025-07-13 04:32:29,645 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:29.644064Z"}
2025-07-13 04:32:29,645 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (9.88s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:02:29.645064Z"}
2025-07-13 04:32:29,645 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.645064Z"}
2025-07-13 04:32:29,656 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.656335Z"}
2025-07-13 04:32:29,657 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.657333Z"}
2025-07-13 04:32:29,658 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.658334Z"}
2025-07-13 04:32:29,663 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.663343Z"}
2025-07-13 04:32:29,664 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.664330Z"}
2025-07-13 04:32:29,664 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.664330Z"}
2025-07-13 04:32:29,665 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.665628Z"}
2025-07-13 04:32:29,666 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.666634Z"}
2025-07-13 04:32:29,666 [INFO] signal_stack: {"event": "Overall Success Rate: 40.0% (4/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.666634Z"}
2025-07-13 04:32:29,667 [INFO] signal_stack: {"event": "\u274c FAIL Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.667633Z"}
2025-07-13 04:32:29,667 [INFO] signal_stack: {"event": "\u274c FAIL Database Schema (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.667633Z"}
2025-07-13 04:32:29,667 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.667633Z"}
2025-07-13 04:32:29,668 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (173.49s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.668633Z"}
2025-07-13 04:32:29,668 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.668633Z"}
2025-07-13 04:32:29,668 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.668633Z"}
2025-07-13 04:32:29,668 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.668633Z"}
2025-07-13 04:32:29,669 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (9.88s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.669633Z"}
2025-07-13 04:32:29,669 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.669633Z"}
2025-07-13 04:32:29,669 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.669633Z"}
2025-07-13 04:32:29,672 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_043229.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.672622Z"}
2025-07-13 04:32:29,673 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:02:29.673644Z"}
2025-07-13 04:35:16,527 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.527783Z"}
2025-07-13 04:35:16,529 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.528783Z"}
2025-07-13 04:35:16,529 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.529752Z"}
2025-07-13 04:35:16,530 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.530771Z"}
2025-07-13 04:35:16,715 [INFO] app.database.connection: Database connection successful
2025-07-13 04:35:16,716 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.716977Z"}
2025-07-13 04:35:16,717 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.717980Z"}
2025-07-13 04:35:16,717 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:35:16,717 [INFO] app.database.connection: Initializing database...
2025-07-13 04:35:16,720 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:35:16,747 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:35:16,748 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:35:16,756 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:35:16,756 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:35:16,761 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:35:16,762 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:35:16,770 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:35:16,771 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:35:16,771 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.771781Z"}
2025-07-13 04:35:16,772 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.772771Z"}
2025-07-13 04:35:16,773 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.772771Z"}
2025-07-13 04:35:16,827 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.827860Z"}
2025-07-13 04:35:16,828 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.828860Z"}
2025-07-13 04:35:16,829 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:05:16.829877Z"}
2025-07-13 04:35:16,841 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:05:16.841308Z"}
2025-07-13 04:35:16,842 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:05:16.842330Z"}
2025-07-13 04:35:17,161 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:05:17.161293Z"}
2025-07-13 04:36:19,397 [INFO] signal_stack: {"event": "Received shutdown signal, stopping pipeline...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:19.397146Z"}
2025-07-13 04:36:28,099 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.099240Z"}
2025-07-13 04:36:28,100 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.100236Z"}
2025-07-13 04:36:28,100 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.100236Z"}
2025-07-13 04:36:28,101 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.101208Z"}
2025-07-13 04:36:28,102 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.102206Z"}
2025-07-13 04:36:28,102 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.102206Z"}
2025-07-13 04:36:28,103 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.103226Z"}
2025-07-13 04:36:28,289 [INFO] app.database.connection: Database connection successful
2025-07-13 04:36:28,290 [ERROR] signal_stack: {"event": "Database validation failed: Textual SQL expression 'SELECT 1 FROM pg_extensio...' should be explicitly declared as text('SELECT 1 FROM pg_extensio...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:06:28.290413Z"}
2025-07-13 04:36:28,291 [ERROR] signal_stack: {"event": "\u274c Database Connection: FAILED (0.19s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:06:28.291414Z"}
2025-07-13 04:36:28,291 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.291414Z"}
2025-07-13 04:36:28,292 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:36:28,292 [INFO] app.database.connection: Initializing database...
2025-07-13 04:36:28,295 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:36:28,322 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:36:28,322 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:36:28,329 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:36:28,330 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:36:28,334 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:36:28,335 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:36:28,354 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:36:28,355 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:36:28,355 [ERROR] signal_stack: {"event": "Schema validation failed: Textual SQL expression 'SELECT 1 FROM information...' should be explicitly declared as text('SELECT 1 FROM information...')", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:06:28.355188Z"}
2025-07-13 04:36:28,356 [ERROR] signal_stack: {"event": "\u274c Database Schema: FAILED (0.06s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:06:28.356189Z"}
2025-07-13 04:36:28,356 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.356189Z"}
2025-07-13 04:36:28,409 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.409637Z"}
2025-07-13 04:36:28,410 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.410657Z"}
2025-07-13 04:36:28,411 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:06:28.411656Z"}
2025-07-13 04:36:28,411 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:06:28.411656Z"}
2025-07-13 04:36:28,412 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:06:28.412641Z"}
2025-07-13 04:36:28,751 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:06:28.751650Z"}
2025-07-13 04:37:01,012 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:07:01.012903Z"}
2025-07-13 04:37:01,014 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:07:01.014220Z"}
2025-07-13 04:37:01,015 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:07:01.015299Z"}
2025-07-13 04:37:01,016 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:07:01.015299Z"}
2025-07-13 04:37:01,016 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.016300Z"}
2025-07-13 04:37:01,017 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (32.61s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.017305Z"}
2025-07-13 04:37:01,018 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.018295Z"}
2025-07-13 04:37:01,018 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:07:01.018927Z"}
2025-07-13 04:37:01,019 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.019945Z"}
2025-07-13 04:37:01,020 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.020964Z"}
2025-07-13 04:37:01,022 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.020964Z"}
2025-07-13 04:37:01,033 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.033376Z"}
2025-07-13 04:37:01,034 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:07:01.034720Z"}
2025-07-13 04:37:01,034 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.034720Z"}
2025-07-13 04:37:01,202 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:37:01.035732 to 2025-07-13 04:37:01.035732", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.202539Z"}
2025-07-13 04:37:01,227 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.227809Z"}
2025-07-13 04:37:01,232 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:37:01.228813 to 2025-07-13 04:37:01.228813", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.232810Z"}
2025-07-13 04:37:01,241 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.241794Z"}
2025-07-13 04:37:01,245 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:37:01.242791 to 2025-07-13 04:37:01.242791", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.245803Z"}
2025-07-13 04:37:01,253 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.253273Z"}
2025-07-13 04:37:01,258 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:37:01.254255 to 2025-07-13 04:37:01.254255", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.258260Z"}
2025-07-13 04:37:01,265 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:07:01.265259Z"}
2025-07-13 04:37:01,284 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.284668Z"}
2025-07-13 04:37:01,285 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.285664Z"}
2025-07-13 04:37:01,285 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.285664Z"}
2025-07-13 04:37:01,286 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.286659Z"}
2025-07-13 04:37:01,287 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.287662Z"}
2025-07-13 04:37:01,288 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.288664Z"}
2025-07-13 04:37:01,289 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:07:01.288664Z"}
2025-07-13 04:37:01,301 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:07:01.301109Z"}
2025-07-13 04:37:01,302 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:07:01.302118Z"}
2025-07-13 04:37:01,303 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:07:01.303119Z"}
2025-07-13 04:38:48,903 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:08:48.903626Z"}
2025-07-13 04:38:48,903 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:08:48.903626Z"}
2025-07-13 04:38:48,904 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:08:48.904636Z"}
2025-07-13 04:38:48,904 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:08:48.904636Z"}
2025-07-13 04:38:48,905 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:08:48.905626Z"}
2025-07-13 04:38:48,906 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:08:48.905626Z"}
2025-07-13 04:38:48,906 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (107.62s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:08:48.906621Z"}
2025-07-13 04:38:48,907 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.907622Z"}
2025-07-13 04:38:48,920 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.919628Z"}
2025-07-13 04:38:48,920 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.920612Z"}
2025-07-13 04:38:48,921 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.921618Z"}
2025-07-13 04:38:48,927 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.927641Z"}
2025-07-13 04:38:48,927 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.927987Z"}
2025-07-13 04:38:48,929 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.927987Z"}
2025-07-13 04:38:48,929 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.929098Z"}
2025-07-13 04:38:48,930 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.930088Z"}
2025-07-13 04:38:48,931 [INFO] signal_stack: {"event": "Overall Success Rate: 40.0% (4/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.930088Z"}
2025-07-13 04:38:48,931 [INFO] signal_stack: {"event": "\u274c FAIL Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.931090Z"}
2025-07-13 04:38:48,932 [INFO] signal_stack: {"event": "\u274c FAIL Database Schema (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.931090Z"}
2025-07-13 04:38:48,932 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.932092Z"}
2025-07-13 04:38:48,933 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (32.61s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.932092Z"}
2025-07-13 04:38:48,933 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.933095Z"}
2025-07-13 04:38:48,934 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.933095Z"}
2025-07-13 04:38:48,934 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.934095Z"}
2025-07-13 04:38:48,935 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (107.62s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.934095Z"}
2025-07-13 04:38:48,935 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.935098Z"}
2025-07-13 04:38:48,936 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.936097Z"}
2025-07-13 04:38:48,938 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_043848.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.937102Z"}
2025-07-13 04:38:48,938 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:48.938105Z"}
2025-07-13 04:38:52,209 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.209719Z"}
2025-07-13 04:38:52,210 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.210728Z"}
2025-07-13 04:38:52,211 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.211706Z"}
2025-07-13 04:38:52,211 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.211706Z"}
2025-07-13 04:38:52,212 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.212707Z"}
2025-07-13 04:38:52,212 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.212707Z"}
2025-07-13 04:38:52,212 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.212707Z"}
2025-07-13 04:38:52,397 [INFO] app.database.connection: Database connection successful
2025-07-13 04:38:52,398 [ERROR] signal_stack: {"event": "Database validation failed: name 'text' is not defined", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:08:52.398460Z"}
2025-07-13 04:38:52,399 [ERROR] signal_stack: {"event": "\u274c Database Connection: FAILED (0.19s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:08:52.399460Z"}
2025-07-13 04:38:52,399 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.399460Z"}
2025-07-13 04:38:52,400 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:38:52,400 [INFO] app.database.connection: Initializing database...
2025-07-13 04:38:52,403 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:38:52,430 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:38:52,431 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:38:52,439 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:38:52,439 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:38:52,445 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:38:52,445 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:38:52,470 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:38:52,470 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:38:52,506 [INFO] signal_stack: {"event": "\u2713 Database schema validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.506565Z"}
2025-07-13 04:38:52,508 [INFO] signal_stack: {"event": "\u2705 Database Schema: PASSED (0.11s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.508566Z"}
2025-07-13 04:38:52,509 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.508566Z"}
2025-07-13 04:38:52,560 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.560884Z"}
2025-07-13 04:38:52,561 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.561875Z"}
2025-07-13 04:38:52,562 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:08:52.562881Z"}
2025-07-13 04:38:52,562 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:08:52.562881Z"}
2025-07-13 04:38:52,563 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:08:52.563859Z"}
2025-07-13 04:38:52,884 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:08:52.884787Z"}
2025-07-13 04:39:24,546 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:09:24.546581Z"}
2025-07-13 04:39:24,547 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:09:24.546581Z"}
2025-07-13 04:39:24,547 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:09:24.547578Z"}
2025-07-13 04:39:24,548 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:09:24.548579Z"}
2025-07-13 04:39:24,549 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.549581Z"}
2025-07-13 04:39:24,550 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (31.99s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.550568Z"}
2025-07-13 04:39:24,550 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.550568Z"}
2025-07-13 04:39:24,551 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:09:24.551571Z"}
2025-07-13 04:39:24,552 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.551571Z"}
2025-07-13 04:39:24,552 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.552565Z"}
2025-07-13 04:39:24,553 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.553574Z"}
2025-07-13 04:39:24,568 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.568216Z"}
2025-07-13 04:39:24,569 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:09:24.569230Z"}
2025-07-13 04:39:24,570 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.570217Z"}
2025-07-13 04:39:24,755 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:39:24.571223 to 2025-07-13 04:39:24.571223", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.755625Z"}
2025-07-13 04:39:24,792 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.792653Z"}
2025-07-13 04:39:24,796 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:39:24.793636 to 2025-07-13 04:39:24.793636", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.796653Z"}
2025-07-13 04:39:24,803 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.803654Z"}
2025-07-13 04:39:24,807 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:39:24.803654 to 2025-07-13 04:39:24.803654", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.807657Z"}
2025-07-13 04:39:24,813 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.813976Z"}
2025-07-13 04:39:24,818 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:39:24.814997 to 2025-07-13 04:39:24.814997", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.816995Z"}
2025-07-13 04:39:24,823 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:09:24.823394Z"}
2025-07-13 04:39:24,837 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.837408Z"}
2025-07-13 04:39:24,838 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.838407Z"}
2025-07-13 04:39:24,838 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.838407Z"}
2025-07-13 04:39:24,839 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.839376Z"}
2025-07-13 04:39:24,840 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.839376Z"}
2025-07-13 04:39:24,841 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.27s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.841387Z"}
2025-07-13 04:39:24,841 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:09:24.841387Z"}
2025-07-13 04:39:24,853 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:09:24.853845Z"}
2025-07-13 04:39:24,853 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:09:24.853845Z"}
2025-07-13 04:39:24,854 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:09:24.854873Z"}
2025-07-13 04:41:21,232 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:21.232986Z"}
2025-07-13 04:41:21,233 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:11:21.233979Z"}
2025-07-13 04:41:21,234 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:11:21.234991Z"}
2025-07-13 04:41:21,234 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:11:21.234991Z"}
2025-07-13 04:41:21,235 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:11:21.235986Z"}
2025-07-13 04:41:21,236 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:21.236982Z"}
2025-07-13 04:41:21,237 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (116.39s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:21.237982Z"}
2025-07-13 04:41:21,238 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.238320Z"}
2025-07-13 04:41:21,250 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.250671Z"}
2025-07-13 04:41:21,251 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.251671Z"}
2025-07-13 04:41:21,251 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.251671Z"}
2025-07-13 04:41:21,257 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.257673Z"}
2025-07-13 04:41:21,258 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.258692Z"}
2025-07-13 04:41:21,259 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.259672Z"}
2025-07-13 04:41:21,259 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.259672Z"}
2025-07-13 04:41:21,260 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.260681Z"}
2025-07-13 04:41:21,261 [INFO] signal_stack: {"event": "Overall Success Rate: 50.0% (5/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.261697Z"}
2025-07-13 04:41:21,261 [INFO] signal_stack: {"event": "\u274c FAIL Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.261697Z"}
2025-07-13 04:41:21,262 [INFO] signal_stack: {"event": "\u2705 PASS Database Schema (0.11s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.262681Z"}
2025-07-13 04:41:21,262 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.262681Z"}
2025-07-13 04:41:21,262 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (31.99s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.262681Z"}
2025-07-13 04:41:21,263 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.263707Z"}
2025-07-13 04:41:21,264 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.264675Z"}
2025-07-13 04:41:21,264 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.27s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.264675Z"}
2025-07-13 04:41:21,265 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (116.39s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.265701Z"}
2025-07-13 04:41:21,265 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.265701Z"}
2025-07-13 04:41:21,266 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.266671Z"}
2025-07-13 04:41:21,268 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_044121.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.268666Z"}
2025-07-13 04:41:21,269 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:21.269667Z"}
2025-07-13 04:41:24,640 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.640711Z"}
2025-07-13 04:41:24,641 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.641727Z"}
2025-07-13 04:41:24,642 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.641727Z"}
2025-07-13 04:41:24,642 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.642740Z"}
2025-07-13 04:41:24,642 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.642740Z"}
2025-07-13 04:41:24,643 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.643739Z"}
2025-07-13 04:41:24,644 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.643739Z"}
2025-07-13 04:41:24,830 [INFO] app.database.connection: Database connection successful
2025-07-13 04:41:24,834 [INFO] signal_stack: {"event": "\u2713 Database connection and TimescaleDB validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.834051Z"}
2025-07-13 04:41:24,836 [INFO] signal_stack: {"event": "\u2705 Database Connection: PASSED (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.836070Z"}
2025-07-13 04:41:24,836 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.836070Z"}
2025-07-13 04:41:24,837 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:41:24,837 [INFO] app.database.connection: Initializing database...
2025-07-13 04:41:24,839 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:41:24,864 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:41:24,864 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:41:24,873 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:41:24,873 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:41:24,879 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:41:24,880 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:41:24,888 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:41:24,888 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:41:24,913 [INFO] signal_stack: {"event": "\u2713 Database schema validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.913088Z"}
2025-07-13 04:41:24,915 [INFO] signal_stack: {"event": "\u2705 Database Schema: PASSED (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.915076Z"}
2025-07-13 04:41:24,915 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.915076Z"}
2025-07-13 04:41:24,968 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.968077Z"}
2025-07-13 04:41:24,970 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.970052Z"}
2025-07-13 04:41:24,970 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:24.970052Z"}
2025-07-13 04:41:24,971 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:11:24.971067Z"}
2025-07-13 04:41:24,971 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:24.971446Z"}
2025-07-13 04:41:25,285 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:25.285317Z"}
2025-07-13 04:41:48,428 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:48.428517Z"}
2025-07-13 04:41:48,429 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:11:48.429535Z"}
2025-07-13 04:41:48,430 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:11:48.430532Z"}
2025-07-13 04:41:48,431 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:11:48.430532Z"}
2025-07-13 04:41:48,431 [ERROR] signal_stack: {"event": "Fyers authentication failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.431512Z"}
2025-07-13 04:41:48,432 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (23.46s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.432513Z"}
2025-07-13 04:41:48,433 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.432513Z"}
2025-07-13 04:41:48,433 [ERROR] app.services.market_data_service: {"event": "Fyers client not authenticated", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:11:48.433510Z"}
2025-07-13 04:41:48,434 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.434514Z"}
2025-07-13 04:41:48,435 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.00s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.435509Z"}
2025-07-13 04:41:48,436 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.435509Z"}
2025-07-13 04:41:48,444 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.444790Z"}
2025-07-13 04:41:48,445 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:11:48.445777Z"}
2025-07-13 04:41:48,446 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.446758Z"}
2025-07-13 04:41:48,627 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:41:48.446758 to 2025-07-13 04:41:48.446758", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.627852Z"}
2025-07-13 04:41:48,651 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.650847Z"}
2025-07-13 04:41:48,655 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:41:48.651875 to 2025-07-13 04:41:48.651875", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.655173Z"}
2025-07-13 04:41:48,662 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.662405Z"}
2025-07-13 04:41:48,667 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:41:48.663404 to 2025-07-13 04:41:48.663404", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.667402Z"}
2025-07-13 04:41:48,673 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.673764Z"}
2025-07-13 04:41:48,677 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:41:48.674765 to 2025-07-13 04:41:48.674765", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.677735Z"}
2025-07-13 04:41:48,684 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:11:48.684104Z"}
2025-07-13 04:41:48,701 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.701510Z"}
2025-07-13 04:41:48,702 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.702504Z"}
2025-07-13 04:41:48,702 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.702504Z"}
2025-07-13 04:41:48,703 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.703521Z"}
2025-07-13 04:41:48,703 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.703521Z"}
2025-07-13 04:41:48,704 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.26s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.704521Z"}
2025-07-13 04:41:48,705 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:11:48.705533Z"}
2025-07-13 04:41:48,716 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:11:48.716853Z"}
2025-07-13 04:41:48,717 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:48.717847Z"}
2025-07-13 04:41:48,717 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:11:48.717847Z"}
2025-07-13 04:42:10,154 [INFO] app.integrations.fyers.auth: {"event": "Authentication cancelled by user", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:12:10.153527Z"}
2025-07-13 04:42:10,154 [ERROR] app.integrations.fyers.auth: {"event": "Failed to get authorization code", "logger": "app.integrations.fyers.auth", "level": "error", "timestamp": "2025-07-12T23:12:10.154531Z"}
2025-07-13 04:42:10,155 [ERROR] app.integrations.fyers.client: {"event": "Failed to get access token", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:12:10.155534Z"}
2025-07-13 04:42:10,156 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:12:10.155534Z"}
2025-07-13 04:42:10,156 [ERROR] app.services.realtime_pipeline: {"event": "Failed to initialize Fyers connection", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:12:10.156543Z"}
2025-07-13 04:42:10,157 [ERROR] signal_stack: {"event": "Pipeline initialization failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:12:10.156543Z"}
2025-07-13 04:42:10,157 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (21.45s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:12:10.157541Z"}
2025-07-13 04:42:10,158 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.158529Z"}
2025-07-13 04:42:10,170 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.170059Z"}
2025-07-13 04:42:10,171 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.171056Z"}
2025-07-13 04:42:10,172 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.172069Z"}
2025-07-13 04:42:10,178 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.01s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.178043Z"}
2025-07-13 04:42:10,179 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.178043Z"}
2025-07-13 04:42:10,179 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.179039Z"}
2025-07-13 04:42:10,179 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.179039Z"}
2025-07-13 04:42:10,180 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.180059Z"}
2025-07-13 04:42:10,181 [INFO] signal_stack: {"event": "Overall Success Rate: 60.0% (6/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.181057Z"}
2025-07-13 04:42:10,181 [INFO] signal_stack: {"event": "\u2705 PASS Database Connection (0.19s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.181057Z"}
2025-07-13 04:42:10,182 [INFO] signal_stack: {"event": "\u2705 PASS Database Schema (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.182059Z"}
2025-07-13 04:42:10,182 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.05s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.182059Z"}
2025-07-13 04:42:10,183 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (23.46s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.183063Z"}
2025-07-13 04:42:10,183 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.00s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.183063Z"}
2025-07-13 04:42:10,184 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.184057Z"}
2025-07-13 04:42:10,185 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.26s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.184057Z"}
2025-07-13 04:42:10,185 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (21.45s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.185058Z"}
2025-07-13 04:42:10,185 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.185058Z"}
2025-07-13 04:42:10,186 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.186059Z"}
2025-07-13 04:42:10,202 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_044210.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.202080Z"}
2025-07-13 04:42:10,203 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:12:10.203068Z"}
2025-07-13 04:54:29,166 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.166636Z"}
2025-07-13 04:54:29,167 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.167635Z"}
2025-07-13 04:54:29,168 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.168646Z"}
2025-07-13 04:54:29,168 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.168646Z"}
2025-07-13 04:54:29,358 [INFO] app.database.connection: Database connection successful
2025-07-13 04:54:29,359 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.359530Z"}
2025-07-13 04:54:29,360 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.360531Z"}
2025-07-13 04:54:29,360 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:54:29,360 [INFO] app.database.connection: Initializing database...
2025-07-13 04:54:29,364 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:54:29,393 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:54:29,394 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:54:29,403 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:54:29,403 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:54:29,409 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:54:29,410 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:54:29,432 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:54:29,433 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:54:29,433 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.433123Z"}
2025-07-13 04:54:29,434 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.434124Z"}
2025-07-13 04:54:29,434 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.434124Z"}
2025-07-13 04:54:29,489 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.489310Z"}
2025-07-13 04:54:29,491 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.491312Z"}
2025-07-13 04:54:29,492 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:24:29.491312Z"}
2025-07-13 04:54:29,502 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:24:29.502684Z"}
2025-07-13 04:54:29,503 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.503682Z"}
2025-07-13 04:54:29,816 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fcallback&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.816450Z"}
2025-07-13 04:54:29,849 [INFO] app.integrations.fyers.auth: {"event": "\n=== Fyers API Authentication ===", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.849536Z"}
2025-07-13 04:54:29,850 [INFO] app.integrations.fyers.auth: {"event": "A browser window will open for you to log in to Fyers.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.850516Z"}
2025-07-13 04:54:29,850 [INFO] app.integrations.fyers.auth: {"event": "After logging in, you will be redirected to Google.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.850516Z"}
2025-07-13 04:54:29,851 [INFO] app.integrations.fyers.auth: {"event": "Copy the auth code from the URL and paste it here.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.851524Z"}
2025-07-13 04:54:29,852 [INFO] app.integrations.fyers.auth: {"event": "\nPlease login in the private browser window that opened.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:24:29.852523Z"}
2025-07-13 04:55:04,411 [INFO] signal_stack: {"event": "Received shutdown signal, stopping pipeline...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:25:04.411642Z"}
2025-07-13 04:56:45,712 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.712140Z"}
2025-07-13 04:56:45,713 [INFO] signal_stack: {"event": "NIFTY REAL-TIME DATA PIPELINE SETUP AND TEST", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.713140Z"}
2025-07-13 04:56:45,714 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.714141Z"}
2025-07-13 04:56:45,714 [INFO] signal_stack: {"event": "Step 1: Checking database connection...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.714141Z"}
2025-07-13 04:56:45,915 [INFO] app.database.connection: Database connection successful
2025-07-13 04:56:45,916 [INFO] signal_stack: {"event": "\u2713 Database connection successful", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.916240Z"}
2025-07-13 04:56:45,916 [INFO] signal_stack: {"event": "\nStep 2: Setting up database...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.916240Z"}
2025-07-13 04:56:45,917 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:56:45,918 [INFO] app.database.connection: Initializing database...
2025-07-13 04:56:45,920 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:56:45,947 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:56:45,947 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:56:45,957 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:56:45,957 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:56:45,963 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:56:45,964 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:56:45,974 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:56:45,976 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:56:45,977 [INFO] signal_stack: {"event": "\u2713 Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.977240Z"}
2025-07-13 04:56:45,978 [INFO] signal_stack: {"event": "\nStep 3: Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.978250Z"}
2025-07-13 04:56:45,979 [INFO] signal_stack: {"event": "Setting up NIFTY symbol...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:45.978250Z"}
2025-07-13 04:56:46,051 [INFO] signal_stack: {"event": "\u2713 NIFTY symbol already exists", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:46.051551Z"}
2025-07-13 04:56:46,052 [INFO] signal_stack: {"event": "\nStep 4: Fetching historical data...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:46.052552Z"}
2025-07-13 04:56:46,053 [INFO] signal_stack: {"event": "Fetching historical data for NIFTY...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:26:46.053546Z"}
2025-07-13 04:56:46,064 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:26:46.064542Z"}
2025-07-13 04:56:46,065 [INFO] app.integrations.fyers.auth: {"event": "Starting Fyers authentication process...", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.065537Z"}
2025-07-13 04:56:46,390 [INFO] app.integrations.fyers.auth: {"event": "Opening browser for authentication: https://api-t1.fyers.in/api/v3/generate-authcode?client_id=SD0YWXHE6D-100&redirect_uri=https%3A%2F%2Ftrade.fyers.in%2Fapi-login%2Fredirect-uri%2Findex.html&response_type=code&state=signal_stack", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.390436Z"}
2025-07-13 04:56:46,425 [INFO] app.integrations.fyers.auth: {"event": "\n=== Fyers API Authentication ===", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.425597Z"}
2025-07-13 04:56:46,426 [INFO] app.integrations.fyers.auth: {"event": "A browser window will open for you to log in to Fyers.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.426587Z"}
2025-07-13 04:56:46,426 [INFO] app.integrations.fyers.auth: {"event": "After logging in, you will be redirected to Google.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.426587Z"}
2025-07-13 04:56:46,427 [INFO] app.integrations.fyers.auth: {"event": "Copy the auth code from the URL and paste it here.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.427591Z"}
2025-07-13 04:56:46,427 [INFO] app.integrations.fyers.auth: {"event": "\nPlease login in the private browser window that opened.", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:46.427591Z"}
2025-07-13 04:56:58,511 [INFO] app.integrations.fyers.auth: {"event": "Authentication files saved to auth", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:58.510960Z"}
2025-07-13 04:56:58,808 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:26:58.808903Z"}
2025-07-13 04:56:58,809 [ERROR] app.integrations.fyers.client: {"event": "Authentication failed: No module named 'fyers_apiv3.FyersModel'", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:26:58.809900Z"}
2025-07-13 04:56:58,810 [ERROR] app.services.market_data_service: {"event": "Failed to initialize Fyers API connection", "logger": "app.services.market_data_service", "level": "error", "timestamp": "2025-07-12T23:26:58.810920Z"}
2025-07-13 04:56:58,810 [ERROR] signal_stack: {"event": "Failed to initialize Fyers connection", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:26:58.810920Z"}
2025-07-13 04:57:32,670 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.670976Z"}
2025-07-13 04:57:32,672 [INFO] signal_stack: {"event": "PRODUCTION SYSTEM VALIDATION", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.672338Z"}
2025-07-13 04:57:32,672 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.672338Z"}
2025-07-13 04:57:32,672 [INFO] signal_stack: {"event": "\u26a0\ufe0f  WARNING: This uses REAL market data and API calls", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.672338Z"}
2025-07-13 04:57:32,673 [INFO] signal_stack: {"event": "\u26a0\ufe0f  Ensure you have valid Fyers credentials", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.673357Z"}
2025-07-13 04:57:32,673 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.673357Z"}
2025-07-13 04:57:32,674 [INFO] signal_stack: {"event": "\n--- Database Connection Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.674363Z"}
2025-07-13 04:57:32,874 [INFO] app.database.connection: Database connection successful
2025-07-13 04:57:32,877 [INFO] signal_stack: {"event": "\u2713 Database connection and TimescaleDB validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.877910Z"}
2025-07-13 04:57:32,878 [INFO] signal_stack: {"event": "\u2705 Database Connection: PASSED (0.20s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.878905Z"}
2025-07-13 04:57:32,879 [INFO] signal_stack: {"event": "\n--- Database Schema Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.879921Z"}
2025-07-13 04:57:32,879 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 04:57:32,880 [INFO] app.database.connection: Initializing database...
2025-07-13 04:57:32,882 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 04:57:32,908 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 04:57:32,908 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 04:57:32,916 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 04:57:32,917 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 04:57:32,921 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 04:57:32,922 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 04:57:32,935 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 04:57:32,935 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 04:57:32,961 [INFO] signal_stack: {"event": "\u2713 Database schema validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.961125Z"}
2025-07-13 04:57:32,962 [INFO] signal_stack: {"event": "\u2705 Database Schema: PASSED (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.962120Z"}
2025-07-13 04:57:32,963 [INFO] signal_stack: {"event": "\n--- Symbol Setup Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:32.963121Z"}
2025-07-13 04:57:33,016 [INFO] signal_stack: {"event": "\u2713 Symbol validated: NIFTY (ID: 1)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:33.016674Z"}
2025-07-13 04:57:33,018 [INFO] signal_stack: {"event": "\u2705 Symbol Setup: PASSED (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:33.018686Z"}
2025-07-13 04:57:33,019 [INFO] signal_stack: {"event": "\n--- Fyers Authentication Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:33.018686Z"}
2025-07-13 04:57:33,019 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:33.019684Z"}
2025-07-13 04:57:33,746 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:27:33.746364Z"}
2025-07-13 04:57:33,747 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:27:33.747364Z"}
2025-07-13 04:57:33,748 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:33.748365Z"}
2025-07-13 04:57:33,749 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-12T23:27:33.748365Z"}
2025-07-13 04:57:33,932 [ERROR] app.integrations.fyers.client: {"event": "Failed to get profile: {'s': 'error', 'code': 500, 'message': 'Looks like you are passing an invalid entry'}", "logger": "app.integrations.fyers.client", "level": "error", "timestamp": "2025-07-12T23:27:33.932427Z"}
2025-07-13 04:57:33,933 [ERROR] signal_stack: {"event": "Failed to retrieve Fyers profile", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:33.933417Z"}
2025-07-13 04:57:33,934 [ERROR] signal_stack: {"event": "\u274c Fyers Authentication: FAILED (0.91s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:33.934429Z"}
2025-07-13 04:57:33,934 [INFO] signal_stack: {"event": "\n--- Historical Data Fetch Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:33.934429Z"}
2025-07-13 04:57:33,935 [INFO] app.services.market_data_service: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-12T23:27:33.935427Z"}
2025-07-13 04:57:33,935 [INFO] app.integrations.fyers.client: {"event": "Fetching historical data for NSE:NIFTY50-INDEX (1) from 2025-07-10 to 2025-07-13", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:33.935427Z"}
2025-07-13 04:57:34,245 [INFO] app.integrations.fyers.client: {"event": "Retrieved 0 historical records for NSE:NIFTY50-INDEX", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:34.245334Z"}
2025-07-13 04:57:34,246 [WARNING] app.services.market_data_service: {"event": "No historical data received for NSE:NIFTY50-INDEX", "logger": "app.services.market_data_service", "level": "warning", "timestamp": "2025-07-12T23:27:34.246336Z"}
2025-07-13 04:57:34,247 [ERROR] signal_stack: {"event": "Historical data fetch failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:34.247350Z"}
2025-07-13 04:57:34,248 [ERROR] signal_stack: {"event": "\u274c Historical Data Fetch: FAILED (0.31s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:34.248339Z"}
2025-07-13 04:57:34,248 [INFO] signal_stack: {"event": "\n--- Data Storage Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.248339Z"}
2025-07-13 04:57:34,257 [ERROR] signal_stack: {"event": "Failed to retrieve stored data", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:34.257705Z"}
2025-07-13 04:57:34,258 [ERROR] signal_stack: {"event": "\u274c Data Storage: FAILED (0.01s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:34.258704Z"}
2025-07-13 04:57:34,258 [INFO] signal_stack: {"event": "\n--- Timeframe Aggregation Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.258704Z"}
2025-07-13 04:57:34,425 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 5m from 2025-07-10 04:57:34.259718 to 2025-07-13 04:57:34.259718", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.425611Z"}
2025-07-13 04:57:34,461 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.460071Z"}
2025-07-13 04:57:34,464 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 15m from 2025-07-10 04:57:34.461082 to 2025-07-13 04:57:34.461082", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.464072Z"}
2025-07-13 04:57:34,471 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.471570Z"}
2025-07-13 04:57:34,475 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 30m from 2025-07-10 04:57:34.471570 to 2025-07-13 04:57:34.471570", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.475565Z"}
2025-07-13 04:57:34,481 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.481580Z"}
2025-07-13 04:57:34,485 [INFO] app.services.aggregation_service: {"event": "Aggregating NIFTY data for 1h from 2025-07-10 04:57:34.481580 to 2025-07-13 04:57:34.481580", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.484570Z"}
2025-07-13 04:57:34,491 [INFO] app.services.aggregation_service: {"event": "Successfully aggregated NIFTY data using stored procedure", "logger": "app.services.aggregation_service", "level": "info", "timestamp": "2025-07-12T23:27:34.491577Z"}
2025-07-13 04:57:34,507 [INFO] signal_stack: {"event": "\u2713 Timeframe aggregation validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.507575Z"}
2025-07-13 04:57:34,508 [INFO] signal_stack: {"event": "  5m: 158 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.508580Z"}
2025-07-13 04:57:34,508 [INFO] signal_stack: {"event": "  15m: 54 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.508580Z"}
2025-07-13 04:57:34,509 [INFO] signal_stack: {"event": "  30m: 28 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.509565Z"}
2025-07-13 04:57:34,509 [INFO] signal_stack: {"event": "  1h: 14 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.509565Z"}
2025-07-13 04:57:34,510 [INFO] signal_stack: {"event": "\u2705 Timeframe Aggregation: PASSED (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.510570Z"}
2025-07-13 04:57:34,511 [INFO] signal_stack: {"event": "\n--- Real-time Data Pipeline Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:34.511573Z"}
2025-07-13 04:57:34,521 [INFO] app.integrations.fyers.client: {"event": "Starting Fyers authentication...", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:34.521995Z"}
2025-07-13 04:57:35,189 [INFO] app.integrations.fyers.auth: {"event": "Fyers authentication successful!", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:27:35.189575Z"}
2025-07-13 04:57:35,190 [INFO] app.integrations.fyers.auth: {"event": "Using existing valid access token", "logger": "app.integrations.fyers.auth", "level": "info", "timestamp": "2025-07-12T23:27:35.190820Z"}
2025-07-13 04:57:35,191 [INFO] app.integrations.fyers.client: {"event": "Fyers authentication successful", "logger": "app.integrations.fyers.client", "level": "info", "timestamp": "2025-07-12T23:27:35.191832Z"}
2025-07-13 04:57:35,191 [INFO] app.services.market_data_service: {"event": "Fyers API connection initialized successfully", "logger": "app.services.market_data_service", "level": "info", "timestamp": "2025-07-12T23:27:35.191832Z"}
2025-07-13 04:57:35,192 [INFO] app.services.realtime_pipeline: {"event": "Real-time data pipeline initialized successfully", "logger": "app.services.realtime_pipeline", "level": "info", "timestamp": "2025-07-12T23:27:35.192831Z"}
2025-07-13 04:57:35,192 [INFO] app.services.realtime_pipeline: {"event": "Starting real-time pipeline for 1 symbols", "logger": "app.services.realtime_pipeline", "level": "info", "timestamp": "2025-07-12T23:27:35.192831Z"}
2025-07-13 04:57:36,851 [INFO] websocket: Websocket connected
2025-07-13 04:57:37,202 [INFO] app.integrations.fyers.websocket_client: {"event": "WebSocket client started", "logger": "app.integrations.fyers.websocket_client", "level": "info", "timestamp": "2025-07-12T23:27:37.202171Z"}
2025-07-13 04:57:37,203 [ERROR] app.integrations.fyers.websocket_client: {"event": "WebSocket not connected. Cannot subscribe to symbols.", "logger": "app.integrations.fyers.websocket_client", "level": "error", "timestamp": "2025-07-12T23:27:37.203169Z"}
2025-07-13 04:57:37,203 [ERROR] app.services.realtime_pipeline: {"event": "Failed to start real-time data streaming", "logger": "app.services.realtime_pipeline", "level": "error", "timestamp": "2025-07-12T23:27:37.203169Z"}
2025-07-13 04:57:37,204 [ERROR] signal_stack: {"event": "Pipeline start failed", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:37.204166Z"}
2025-07-13 04:57:37,205 [ERROR] signal_stack: {"event": "\u274c Real-time Data Pipeline: FAILED (2.69s)", "logger": "signal_stack", "level": "error", "timestamp": "2025-07-12T23:27:37.205151Z"}
2025-07-13 04:57:37,205 [INFO] signal_stack: {"event": "\n--- Data Integrity Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.205511Z"}
2025-07-13 04:57:37,218 [INFO] signal_stack: {"event": "\u2713 Data integrity validated", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.218048Z"}
2025-07-13 04:57:37,220 [INFO] signal_stack: {"event": "\u2705 Data Integrity: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.219050Z"}
2025-07-13 04:57:37,221 [INFO] signal_stack: {"event": "\n--- Performance Metrics Validation ---", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.221032Z"}
2025-07-13 04:57:37,227 [INFO] signal_stack: {"event": "\u2713 Performance validated: Query took 0.00s for 0 records", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.226017Z"}
2025-07-13 04:57:37,227 [INFO] signal_stack: {"event": "\u2705 Performance Metrics: PASSED (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.227380Z"}
2025-07-13 04:57:37,227 [INFO] signal_stack: {"event": "\n================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.227380Z"}
2025-07-13 04:57:37,228 [INFO] signal_stack: {"event": "VALIDATION REPORT", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.228392Z"}
2025-07-13 04:57:37,228 [INFO] signal_stack: {"event": "================================================================================", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.228392Z"}
2025-07-13 04:57:37,229 [INFO] signal_stack: {"event": "Overall Success Rate: 60.0% (6/10)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.229406Z"}
2025-07-13 04:57:37,229 [INFO] signal_stack: {"event": "\u2705 PASS Database Connection (0.20s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.229406Z"}
2025-07-13 04:57:37,230 [INFO] signal_stack: {"event": "\u2705 PASS Database Schema (0.08s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.230402Z"}
2025-07-13 04:57:37,230 [INFO] signal_stack: {"event": "\u2705 PASS Symbol Setup (0.06s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.230402Z"}
2025-07-13 04:57:37,231 [INFO] signal_stack: {"event": "\u274c FAIL Fyers Authentication (0.91s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.231401Z"}
2025-07-13 04:57:37,231 [INFO] signal_stack: {"event": "\u274c FAIL Historical Data Fetch (0.31s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.231401Z"}
2025-07-13 04:57:37,232 [INFO] signal_stack: {"event": "\u274c FAIL Data Storage (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.232403Z"}
2025-07-13 04:57:37,232 [INFO] signal_stack: {"event": "\u2705 PASS Timeframe Aggregation (0.25s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.232403Z"}
2025-07-13 04:57:37,233 [INFO] signal_stack: {"event": "\u274c FAIL Real-time Data Pipeline (2.69s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.233390Z"}
2025-07-13 04:57:37,234 [INFO] signal_stack: {"event": "\u2705 PASS Data Integrity (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.234396Z"}
2025-07-13 04:57:37,235 [INFO] signal_stack: {"event": "\u2705 PASS Performance Metrics (0.01s)", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.234396Z"}
2025-07-13 04:57:37,238 [INFO] signal_stack: {"event": "Detailed report saved to: validation_report_20250713_045737.json", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.237391Z"}
2025-07-13 04:57:37,239 [INFO] signal_stack: {"event": "\u274c MULTIPLE VALIDATIONS FAILED - SYSTEM NOT READY", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-12T23:27:37.238391Z"}
2025-07-13 04:57:37,793 [ERROR] app.integrations.fyers.websocket_client: {"event": "WebSocket connection error: FyersWebSocketClient._on_error() missing 1 required positional argument: 'error'", "logger": "app.integrations.fyers.websocket_client", "level": "error", "timestamp": "2025-07-12T23:27:37.793581Z"}
2025-07-13 15:56:38,220 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:26:38.220154Z"}
2025-07-13 15:56:38,482 [INFO] app.database.connection: Database connection successful
2025-07-13 15:56:38,483 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:56:38,483 [INFO] app.database.connection: Initializing database...
2025-07-13 15:56:38,490 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:56:38,572 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:56:38,573 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:56:38,584 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:56:38,585 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:56:38,592 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:56:38,592 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:56:38,603 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:56:38,603 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:56:38,604 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:26:38.604490Z"}
2025-07-13 15:56:38,605 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:26:38.604490Z"}
2025-07-13 15:57:08,214 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:27:08.214343Z"}
2025-07-13 15:57:08,407 [INFO] app.database.connection: Database connection successful
2025-07-13 15:57:08,407 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:57:08,408 [INFO] app.database.connection: Initializing database...
2025-07-13 15:57:08,411 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:57:08,440 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:57:08,440 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:57:08,449 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:57:08,450 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:57:08,456 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:57:08,456 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:57:08,477 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:57:08,478 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:57:08,478 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:27:08.478969Z"}
2025-07-13 15:57:08,479 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:27:08.479971Z"}
2025-07-13 15:58:13,957 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:13.957725Z"}
2025-07-13 15:58:18,350 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:18.350169Z"}
2025-07-13 15:58:18,641 [INFO] app.database.connection: Database connection successful
2025-07-13 15:58:18,641 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:58:18,641 [INFO] app.database.connection: Initializing database...
2025-07-13 15:58:18,645 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:58:18,676 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:58:18,678 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:58:18,687 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:58:18,688 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:58:18,694 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:58:18,694 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:58:18,703 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:58:18,703 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:58:18,704 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:18.704451Z"}
2025-07-13 15:58:18,705 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:18.705430Z"}
2025-07-13 15:58:19,389 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:19.389508Z"}
2025-07-13 15:58:23,620 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:23.620456Z"}
2025-07-13 15:58:23,812 [INFO] app.database.connection: Database connection successful
2025-07-13 15:58:23,813 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:58:23,813 [INFO] app.database.connection: Initializing database...
2025-07-13 15:58:23,817 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:58:23,848 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:58:23,849 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:58:23,858 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:58:23,858 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:58:23,866 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:58:23,867 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:58:23,881 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:58:23,881 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:58:23,882 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:23.881989Z"}
2025-07-13 15:58:23,882 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:28:23.882999Z"}
2025-07-13 15:59:55,500 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:29:55.500475Z"}
2025-07-13 15:59:59,702 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:29:59.702864Z"}
2025-07-13 15:59:59,888 [INFO] app.database.connection: Database connection successful
2025-07-13 15:59:59,888 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 15:59:59,889 [INFO] app.database.connection: Initializing database...
2025-07-13 15:59:59,893 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 15:59:59,923 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 15:59:59,924 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 15:59:59,933 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 15:59:59,934 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 15:59:59,940 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 15:59:59,941 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 15:59:59,948 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 15:59:59,949 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 15:59:59,949 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:29:59.949450Z"}
2025-07-13 15:59:59,950 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:29:59.950448Z"}
2025-07-13 16:00:29,909 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:30:29.909041Z"}
2025-07-13 16:00:35,477 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:30:35.476461Z"}
2025-07-13 16:00:35,859 [INFO] app.database.connection: Database connection successful
2025-07-13 16:00:35,863 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 16:00:35,866 [INFO] app.database.connection: Initializing database...
2025-07-13 16:00:35,872 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 16:00:35,924 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 16:00:35,932 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 16:00:35,946 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 16:00:35,955 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 16:00:35,970 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 16:00:35,978 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 16:00:35,995 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 16:00:35,999 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 16:00:36,005 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:30:36.005565Z"}
2025-07-13 16:00:36,007 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:30:36.007568Z"}
2025-07-13 16:01:05,632 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:31:05.632204Z"}
2025-07-13 16:01:09,696 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:31:09.695372Z"}
2025-07-13 16:01:09,881 [INFO] app.database.connection: Database connection successful
2025-07-13 16:01:09,882 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 16:01:09,882 [INFO] app.database.connection: Initializing database...
2025-07-13 16:01:09,886 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 16:01:09,917 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 16:01:09,917 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 16:01:09,926 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 16:01:09,927 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 16:01:09,933 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 16:01:09,933 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 16:01:09,941 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 16:01:09,942 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 16:01:09,942 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:31:09.942654Z"}
2025-07-13 16:01:09,943 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:31:09.943652Z"}
2025-07-13 16:02:01,889 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:01.889877Z"}
2025-07-13 16:02:06,409 [INFO] signal_stack: {"event": "Starting Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:06.408869Z"}
2025-07-13 16:02:06,593 [INFO] app.database.connection: Database connection successful
2025-07-13 16:02:06,593 [INFO] app.database.init_db: Starting complete database setup...
2025-07-13 16:02:06,594 [INFO] app.database.connection: Initializing database...
2025-07-13 16:02:06,598 [INFO] app.database.connection: TimescaleDB extension already exists
2025-07-13 16:02:06,623 [INFO] app.database.connection: Database initialization completed successfully
2025-07-13 16:02:06,624 [INFO] app.database.init_db: Creating TimescaleDB hypertables...
2025-07-13 16:02:06,632 [INFO] app.database.init_db: TimescaleDB hypertables created successfully
2025-07-13 16:02:06,633 [INFO] app.database.init_db: Creating additional indexes...
2025-07-13 16:02:06,638 [INFO] app.database.init_db: Additional indexes created successfully
2025-07-13 16:02:06,639 [INFO] app.database.init_db: Creating stored procedures...
2025-07-13 16:02:06,666 [INFO] app.database.init_db: Stored procedures created successfully
2025-07-13 16:02:06,667 [INFO] app.database.init_db: Database setup completed successfully
2025-07-13 16:02:06,667 [INFO] signal_stack: {"event": "Database setup completed", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:06.667196Z"}
2025-07-13 16:02:06,668 [INFO] signal_stack: {"event": "Application startup completed successfully", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:06.668194Z"}
2025-07-13 16:02:36,544 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:32:36.544450Z"}
2025-07-13 16:04:55,504 [INFO] signal_stack: {"event": "Shutting down Signal Stack Trading Platform...", "logger": "signal_stack", "level": "info", "timestamp": "2025-07-13T10:34:55.504345Z"}
