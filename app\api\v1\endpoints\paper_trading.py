"""
Paper trading API endpoints.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api.dependencies import validate_symbol
from app.api.schemas.paper_trading import (
    CreateSessionRequest,
    CreateSessionResponse,
    SessionListResponse,
    SessionInfo,
    AddStrategyRequest,
    PlaceOrderRequest,
    PlaceOrderResponse,
    SessionStatusResponse,
    PerformanceResponse,
    SimulateSignalRequest,
    SessionHistoryResponse,
    ExportDataResponse,
    PositionInfo,
    OrderInfo,
    TradeRecord
)
from app.services.paper_trading_service import PaperTradingService
from app.database.connection import get_db
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


def get_paper_trading_service(db: Session = Depends(get_db)) -> PaperTradingService:
    """Get paper trading service instance."""
    return PaperTradingService(db)


@router.post("/sessions", response_model=CreateSessionResponse)
async def create_session(
    request: CreateSessionRequest,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Create a new paper trading session."""
    try:
        if request.initial_cash <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Initial cash must be positive"
            )
        
        session_id = service.create_session(
            name=request.name,
            initial_cash=request.initial_cash,
            commission_rate=request.commission_rate,
            update_interval=request.update_interval
        )
        
        return CreateSessionResponse(
            session_id=session_id,
            name=request.name,
            initial_cash=request.initial_cash
        )
        
    except Exception as e:
        logger.error(f"Error creating paper trading session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create paper trading session"
        )


@router.get("/sessions", response_model=SessionListResponse)
async def get_sessions(
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Get list of active paper trading sessions."""
    try:
        sessions = service.get_active_sessions()
        
        session_list = [
            SessionInfo(**session) for session in sessions
        ]
        
        return SessionListResponse(sessions=session_list)
        
    except Exception as e:
        logger.error(f"Error getting sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sessions"
        )


@router.post("/sessions/{session_id}/start")
async def start_session(
    session_id: str,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Start a paper trading session."""
    try:
        success = service.start_session(session_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        return {"success": True, "message": "Session started"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start session"
        )


@router.post("/sessions/{session_id}/stop")
async def stop_session(
    session_id: str,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Stop a paper trading session."""
    try:
        success = service.stop_session(session_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        return {"success": True, "message": "Session stopped"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop session"
        )


@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: str,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Delete a paper trading session."""
    try:
        success = service.delete_session(session_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        return {"success": True, "message": "Session deleted"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete session"
        )


@router.post("/sessions/{session_id}/strategies")
async def add_strategy(
    session_id: str,
    request: AddStrategyRequest,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Add a strategy to a paper trading session."""
    try:
        # Validate symbols
        validated_symbols = [validate_symbol(symbol) for symbol in request.symbols]
        
        success = service.add_strategy_to_session(
            session_id=session_id,
            strategy_type=request.strategy_type,
            symbols=validated_symbols,
            parameters=request.parameters
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to add strategy to session"
            )
        
        return {"success": True, "message": "Strategy added to session"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding strategy to session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add strategy"
        )


@router.post("/sessions/{session_id}/orders", response_model=PlaceOrderResponse)
async def place_order(
    session_id: str,
    request: PlaceOrderRequest,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Place an order in a paper trading session."""
    try:
        symbol = validate_symbol(request.symbol)
        
        if request.quantity <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Order quantity must be positive"
            )
        
        order_id = service.place_order(
            session_id=session_id,
            symbol=symbol,
            side=request.side.lower(),
            order_type=request.order_type.lower(),
            quantity=request.quantity,
            price=request.price
        )
        
        if not order_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to place order"
            )
        
        return PlaceOrderResponse(
            order_id=order_id,
            symbol=symbol,
            side=request.side,
            quantity=request.quantity,
            status="pending"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error placing order in session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to place order"
        )


@router.delete("/sessions/{session_id}/orders/{order_id}")
async def cancel_order(
    session_id: str,
    order_id: str,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Cancel an order in a paper trading session."""
    try:
        success = service.cancel_order(session_id, order_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found or cannot be cancelled"
            )
        
        return {"success": True, "message": "Order cancelled"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling order {order_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel order"
        )


@router.post("/sessions/{session_id}/positions/{symbol}/close")
async def close_position(
    session_id: str,
    symbol: str,
    quantity: Optional[int] = Query(None),
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Close a position in a paper trading session."""
    try:
        symbol = validate_symbol(symbol)
        
        success = service.close_position(session_id, symbol, quantity)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Position not found or cannot be closed"
            )
        
        return {"success": True, "message": "Position closed"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error closing position {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to close position"
        )


@router.get("/sessions/{session_id}/status", response_model=SessionStatusResponse)
async def get_session_status(
    session_id: str,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Get session status and portfolio information."""
    try:
        status_data = service.get_session_status(session_id)
        
        if not status_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        portfolio = status_data['portfolio_status']
        
        # Convert positions to response format
        positions = [
            PositionInfo(**pos) for pos in portfolio.get('positions', {}).values()
        ]
        
        # Convert orders to response format
        orders = [
            OrderInfo(**order) for order in portfolio.get('pending_orders', [])
        ]
        
        return SessionStatusResponse(
            session_id=session_id,
            is_running=status_data['engine_status']['is_running'],
            cash=portfolio['cash'],
            total_value=portfolio['total_value'],
            total_pnl=portfolio['performance']['total_pnl'],
            positions=positions,
            pending_orders=orders,
            performance=portfolio['performance']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session status {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get session status"
        )


@router.get("/sessions/{session_id}/performance", response_model=PerformanceResponse)
async def get_session_performance(
    session_id: str,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Get session performance report."""
    try:
        performance = service.get_session_performance(session_id)
        
        if not performance:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        # Convert trades to response format
        recent_trades = [
            TradeRecord(**trade) for trade in performance.get('recent_trades', [])
        ]
        
        return PerformanceResponse(
            session_id=session_id,
            engine_status=performance['engine_status'],
            portfolio_performance=performance['portfolio_performance'],
            strategy_performance=performance['strategy_performance'],
            recent_trades=recent_trades,
            value_history=performance['value_history']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session performance {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get session performance"
        )


@router.post("/sessions/{session_id}/simulate-signal")
async def simulate_signal(
    session_id: str,
    request: SimulateSignalRequest,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Simulate a trading signal for testing."""
    try:
        symbol = validate_symbol(request.symbol)
        
        success = service.simulate_signal(
            session_id=session_id,
            symbol=symbol,
            signal_type=request.signal_type.lower(),
            price=request.price,
            quantity=request.quantity
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to simulate signal"
            )
        
        return {"success": True, "message": "Signal simulated"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error simulating signal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to simulate signal"
        )


@router.post("/sessions/{session_id}/reset")
async def reset_session(
    session_id: str,
    initial_cash: Optional[float] = Query(None),
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Reset a paper trading session."""
    try:
        success = service.reset_session(session_id, initial_cash)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        return {"success": True, "message": "Session reset"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resetting session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset session"
        )


@router.get("/sessions/{session_id}/history", response_model=SessionHistoryResponse)
async def get_session_history(
    session_id: str,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Get session trading history."""
    try:
        history = service.get_session_history(session_id)
        
        if 'error' in history:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=history['error']
            )
        
        # Convert to response format
        trades = [TradeRecord(**trade) for trade in history['trades']]
        orders = [OrderInfo(**order) for order in history['order_history']]
        
        return SessionHistoryResponse(
            session_id=session_id,
            trades=trades,
            order_history=orders,
            value_history=history['value_history'],
            performance_summary=history['performance_summary']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session history {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get session history"
        )


@router.get("/sessions/{session_id}/export", response_model=ExportDataResponse)
async def export_session_data(
    session_id: str,
    service: PaperTradingService = Depends(get_paper_trading_service)
):
    """Export complete session data."""
    try:
        data = service.export_session_data(session_id)
        
        if not data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        return ExportDataResponse(
            session_id=session_id,
            export_timestamp=data['export_timestamp'],
            data=data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting session data {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export session data"
        )
